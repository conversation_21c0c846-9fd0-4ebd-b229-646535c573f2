#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能多平台数据爬取工具 - 简化版本
移除numpy和pandas依赖，使用纯Python实现
"""

import json
import sys
import os
import time
import requests
import urllib.parse
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 设置 PyQt5 环境变量
import PyQt5
pyqt_path = os.path.dirname(PyQt5.__file__)
os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = os.path.join(pyqt_path, 'Qt5', 'plugins', 'platforms')
os.environ['QT_PLUGIN_PATH'] = os.path.join(pyqt_path, 'Qt5', 'plugins')

# 添加 DLL 路径
qt_bin_path = os.path.join(pyqt_path, 'Qt5', 'bin')
if os.path.exists(qt_bin_path):
    os.environ['PATH'] = qt_bin_path + os.pathsep + os.environ.get('PATH', '')

from bs4 import BeautifulSoup
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QLineEdit, QPushButton, QTextEdit, 
                             QProgressBar, QFileDialog, QMessageBox, QSpinBox,
                             QDialog, QTabWidget, QFormLayout, QFrame, QSettings)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QFont, QIcon

# 简单的Excel写入类，替代pandas
class SimpleExcelWriter:
    def __init__(self, filename):
        self.filename = filename
        self.data = []
        
    def add_row(self, row_data):
        """添加一行数据"""
        self.data.append(row_data)
    
    def save(self):
        """保存为CSV格式（Excel可以打开）"""
        try:
            import csv
            with open(self.filename.replace('.xlsx', '.csv'), 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                for row in self.data:
                    writer.writerow(row)
            return True
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False

class ConfigDialog(QDialog):
    """配置对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("⚙️ 系统配置")
        self.setModal(True)
        self.setFixedSize(650, 500)  # 减小窗口大小
        self.settings = QSettings("DataScrapingTool", "Config")
        self.init_ui()
        self.load_settings()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 赛盈平台标签页
        saiyii_tab = self.create_saiyii_config_tab()
        self.tab_widget.addTab(saiyii_tab, "🏪 赛盈平台")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        test_btn = QPushButton("🔍 测试连接")
        test_btn.setStyleSheet(self.get_button_style("#3498db"))
        test_btn.clicked.connect(self.test_connection)
        button_layout.addWidget(test_btn)
        
        button_layout.addStretch()
        
        ok_btn = QPushButton("OK")
        ok_btn.setStyleSheet(self.get_button_style("#27ae60"))
        ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(ok_btn)
        
        cancel_btn = QPushButton("Cancel")
        cancel_btn.setStyleSheet(self.get_button_style("#e74c3c"))
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)

    def create_saiyii_config_tab(self):
        """创建赛盈平台配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 说明文本
        info_label = QLabel("🏪 赛盈平台API配置")
        info_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        info_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(info_label)

        desc_label = QLabel("配置赛盈平台的API访问参数，用于获取产品数据")
        desc_label.setStyleSheet("color: #7f8c8d; margin-bottom: 15px;")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        # 配置表单
        form_layout = QFormLayout()
        form_layout.setSpacing(12)

        # saiyii_base_url
        self.saiyii_base_url_input = QLineEdit()
        self.saiyii_base_url_input.setPlaceholderText("https://api.saleyee.com")
        self.saiyii_base_url_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("🌐 saiyii_base_url:", self.saiyii_base_url_input)

        # saiyii_app_token
        self.saiyii_app_token_input = QLineEdit()
        self.saiyii_app_token_input.setPlaceholderText("ZocsKv7BS5vH3orQGxDQOQ==")
        self.saiyii_app_token_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("🔑 saiyii_app_token:", self.saiyii_app_token_input)

        # saiyii_app_key
        self.saiyii_app_key_input = QLineEdit()
        self.saiyii_app_key_input.setPlaceholderText("SnBIRFqo")
        self.saiyii_app_key_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("🗝️ saiyii_app_key:", self.saiyii_app_key_input)

        layout.addLayout(form_layout)

        # 帮助信息
        help_frame = QFrame()
        help_frame.setStyleSheet("QFrame { background: #f8f9fa; border-radius: 6px; padding: 10px; }")
        help_layout = QVBoxLayout(help_frame)

        help_title = QLabel("💡 获取API参数的方法:")
        help_title.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        help_title.setStyleSheet("color: #495057;")
        help_layout.addWidget(help_title)

        help_text = QLabel("""
1. 登录赛盈平台开发者中心
2. 进入API管理页面
3. 创建或查看现有应用
4. 复制App Token和App Key
5. 将参数填入上方输入框
        """.strip())
        help_text.setStyleSheet("color: #6c757d; font-size: 10px; line-height: 1.4;")
        help_text.setWordWrap(True)
        help_layout.addWidget(help_text)

        layout.addWidget(help_frame)
        layout.addStretch()

        return widget

    def get_input_style(self):
        """获取输入框样式"""
        return """
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px 12px;
                background-color: white;
                font-size: 12px;
                color: #2c3e50;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QLineEdit:hover {
                border-color: #7f8c8d;
            }
        """

    def get_button_style(self, color):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background-color: {color};
                border: none;
                color: white;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 4px;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color, 0.9)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """

    def darken_color(self, color, factor):
        """使颜色变暗"""
        # 简单的颜色变暗实现
        color_map = {
            "#3498db": "#2980b9",
            "#27ae60": "#229954", 
            "#e74c3c": "#c0392b"
        }
        return color_map.get(color, color)

    def test_connection(self):
        """测试连接"""
        try:
            base_url = self.saiyii_base_url_input.text().strip()
            app_token = self.saiyii_app_token_input.text().strip()
            app_key = self.saiyii_app_key_input.text().strip()
            
            if not base_url:
                QMessageBox.warning(self, "配置检查", "⚠️ 请填写API基础地址")
                return
                
            if not app_token:
                QMessageBox.warning(self, "配置检查", "⚠️ 请填写App Token")
                return
                
            if not app_key:
                QMessageBox.warning(self, "配置检查", "⚠️ 请填写App Key")
                return
            
            # 简单的连接测试
            response = requests.get(base_url, timeout=5)
            if response.status_code in [200, 404, 405]:
                QMessageBox.information(self, "连接测试", "✅ 赛盈平台连接正常！\n服务器响应正常")
            else:
                QMessageBox.warning(self, "连接测试", f"⚠️ 服务器响应异常\n状态码: {response.status_code}")
                
        except requests.exceptions.Timeout:
            QMessageBox.critical(self, "连接测试", "❌ 连接超时\n请检查网络连接或API地址")
        except requests.exceptions.ConnectionError:
            QMessageBox.critical(self, "连接测试", "❌ 无法连接到服务器\n请检查API地址是否正确")
        except Exception as e:
            QMessageBox.critical(self, "连接测试", f"❌ 连接测试失败\n错误信息: {str(e)}")

    def load_settings(self):
        """加载设置"""
        # 赛盈平台设置
        self.saiyii_base_url_input.setText(
            self.settings.value("saiyii/base_url", "https://api.saleyee.com")
        )
        self.saiyii_app_token_input.setText(
            self.settings.value("saiyii/app_token", "ZocsKv7BS5vH3orQGxDQOQ==")
        )
        self.saiyii_app_key_input.setText(
            self.settings.value("saiyii/app_key", "SnBIRFqo")
        )

    def save_settings(self):
        """保存设置"""
        # 赛盈平台设置
        self.settings.setValue("saiyii/base_url", self.saiyii_base_url_input.text())
        self.settings.setValue("saiyii/app_token", self.saiyii_app_token_input.text())
        self.settings.setValue("saiyii/app_key", self.saiyii_app_key_input.text())

    def accept(self):
        """确定按钮"""
        self.save_settings()
        super().accept()

    def get_config(self):
        """获取配置"""
        return {
            'saiyii': {
                'base_url': self.saiyii_base_url_input.text(),
                'app_token': self.saiyii_app_token_input.text(),
                'app_key': self.saiyii_app_key_input.text(),
            }
        }

class DataScrapingTool(QMainWindow):
    """主窗口类"""
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🚀 智能多平台数据爬取工具 v1.0")
        self.setGeometry(100, 100, 1000, 700)
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("🚀 智能多平台数据爬取工具")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
        layout.addWidget(title_label)
        
        # 配置按钮
        config_btn = QPushButton("⚙️ 系统配置")
        config_btn.clicked.connect(self.show_config)
        layout.addWidget(config_btn)
        
        # SKU输入区域
        sku_label = QLabel("📝 请输入SKU列表（每行一个）:")
        layout.addWidget(sku_label)
        
        self.sku_input = QTextEdit()
        self.sku_input.setPlaceholderText("请输入SKU，每行一个\n例如：\nW1239138471\n32791415\nDL000649AAB")
        layout.addWidget(self.sku_input)
        
        # 控制区域
        control_layout = QHBoxLayout()
        
        # 保存路径
        path_label = QLabel("💾 保存路径:")
        control_layout.addWidget(path_label)
        
        self.path_input = QLineEdit()
        self.path_input.setPlaceholderText("选择保存文件路径")
        control_layout.addWidget(self.path_input)
        
        browse_btn = QPushButton("📁 浏览")
        browse_btn.clicked.connect(self.browse_file)
        control_layout.addWidget(browse_btn)
        
        layout.addLayout(control_layout)
        
        # 开始按钮
        start_btn = QPushButton("🚀 开始处理")
        start_btn.clicked.connect(self.start_processing)
        layout.addWidget(start_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)
        
        # 日志区域
        log_label = QLabel("📋 处理日志:")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        layout.addWidget(self.log_text)

    def show_config(self):
        """显示配置对话框"""
        dialog = ConfigDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.log("✅ 配置已保存")

    def browse_file(self):
        """浏览文件"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存文件", "", "CSV files (*.csv);;All files (*.*)"
        )
        if filename:
            self.path_input.setText(filename)

    def start_processing(self):
        """开始处理"""
        skus = self.sku_input.toPlainText().strip().split('\n')
        skus = [sku.strip() for sku in skus if sku.strip()]
        
        if not skus:
            QMessageBox.warning(self, "输入错误", "请输入至少一个SKU")
            return
            
        if not self.path_input.text():
            QMessageBox.warning(self, "路径错误", "请选择保存路径")
            return
        
        self.log(f"🚀 开始处理 {len(skus)} 个SKU")
        
        # 创建Excel写入器
        writer = SimpleExcelWriter(self.path_input.text())
        writer.add_row(['SKU', '平台', '状态', '处理时间'])
        
        # 简单处理（演示）
        for i, sku in enumerate(skus):
            platform = "GIGA" if any(c.isalpha() for c in sku) else "赛盈"
            writer.add_row([sku, platform, "已处理", time.strftime("%Y-%m-%d %H:%M:%S")])
            self.progress_bar.setValue(int((i + 1) / len(skus) * 100))
            self.log(f"✅ 处理完成: {sku} ({platform}平台)")
            QApplication.processEvents()  # 更新界面
        
        # 保存文件
        if writer.save():
            self.log(f"💾 数据已保存到: {self.path_input.text()}")
            QMessageBox.information(self, "处理完成", f"✅ 成功处理 {len(skus)} 个SKU\n文件已保存")
        else:
            QMessageBox.critical(self, "保存失败", "❌ 文件保存失败")

    def log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("智能多平台数据爬取工具")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = DataScrapingTool()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
