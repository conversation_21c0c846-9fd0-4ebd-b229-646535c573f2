@echo off
echo Starting build process...
echo.

REM Change to script directory
cd /d "%~dp0"

REM Check Python
python --version
if %errorlevel% neq 0 (
    echo Python not found
    pause
    exit /b 1
)

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt

REM Clean old files
echo Cleaning old files...
if exist dist rmdir /s /q dist
if exist build rmdir /s /q build

REM Build with PyInstaller
echo Building application...
pyinstaller --onefile --windowed --name "DataScrapingTool" main.py

REM Check result
if exist "dist\DataScrapingTool.exe" (
    echo Build successful!
    echo Output: dist\DataScrapingTool.exe
) else (
    echo Build failed!
)

pause
