@echo off
echo Building Smart Data Scraping Tool...
echo.

cd /d "%~dp0"

echo [1/4] Installing dependencies...
pip install -r requirements.txt

echo [2/4] Cleaning old files...
if exist dist rmdir /s /q dist
if exist build rmdir /s /q build

echo [3/4] Building executable...
pyinstaller --onefile --windowed --name "SmartDataScrapingTool" --clean --noconfirm main.py

echo [4/4] Checking result...
if exist "dist\SmartDataScrapingTool.exe" (
    echo.
    echo Build successful!
    echo Output: dist\SmartDataScrapingTool.exe
    
    for %%A in ("dist\SmartDataScrapingTool.exe") do (
        echo Size: %%~zA bytes
    )
    
    echo.
    echo Features:
    echo - GIGA platform support
    echo - Saiyii platform API integration  
    echo - Modern UI with PyQt5
    echo - Multi-threading support
    echo - Excel export functionality
    echo.
    
    explorer dist
) else (
    echo Build failed!
)

pause
