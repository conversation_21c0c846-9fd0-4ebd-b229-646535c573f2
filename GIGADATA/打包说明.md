# 网页数据爬取工具 - 打包说明

## 打包准备

### 1. 安装Python环境
确保您的系统已安装Python 3.7或更高版本。

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

## 打包方法

### 方法一：使用脚本打包（推荐）

#### Windows用户
双击运行 `build.bat` 文件，或在命令行中执行：
```cmd
build.bat
```

#### Linux/Mac用户
在终端中执行：
```bash
chmod +x build.sh
./build.sh
```

### 方法二：手动打包

#### 1. 安装PyInstaller
```bash
pip install pyinstaller
```

#### 2. 执行打包命令
```bash
# 使用配置文件打包
pyinstaller build.spec

# 或者使用命令行参数打包
pyinstaller --onefile --windowed --name="网页数据爬取工具" main.py
```

## 打包结果

打包完成后，在 `dist` 文件夹中会生成以下文件：
- `网页数据爬取工具.exe` (Windows) 或 `网页数据爬取工具` (Linux/Mac)
- 相关的依赖库文件

## 分发程序

### 单文件分发
如果使用 `--onefile` 参数，会生成一个独立的可执行文件，可以直接复制到目标电脑运行。

### 文件夹分发
默认情况下会生成一个包含所有依赖的文件夹，需要将整个 `dist` 文件夹复制到目标电脑。

## 注意事项

1. **文件大小**: 打包后的程序可能比较大（100-200MB），这是正常现象，因为包含了所有Python依赖。

2. **防病毒软件**: 某些防病毒软件可能会误报，这是PyInstaller打包程序的常见问题，属正常现象。

3. **运行权限**: 在某些系统上可能需要管理员权限运行。

4. **数据库连接**: 确保目标电脑能够访问MySQL数据库（网络连接、防火墙设置等）。

5. **首次运行**: 打包后的程序首次运行可能会比较慢，这是正常现象。

## 故障排除

### 打包失败
- 检查Python版本是否兼容
- 确保所有依赖都已正确安装
- 查看错误信息，可能缺少某些系统库

### 运行错误
- 检查是否缺少Microsoft Visual C++ Redistributable
- 确保目标系统架构匹配（32位/64位）
- 查看是否有权限问题

### 程序无法启动
- 尝试在命令行中运行，查看错误信息
- 检查防病毒软件是否阻止运行
- 确保所有文件都已正确复制

## 自定义打包

如果需要自定义打包配置，可以修改 `build.spec` 文件：

- 添加图标: 修改 `icon` 参数
- 包含额外文件: 修改 `datas` 参数
- 修改程序名称: 修改 `name` 参数
- 显示控制台: 将 `console` 设为 `True`

## 技术支持

如果在打包过程中遇到问题，请检查：
1. Python版本兼容性
2. 依赖包版本
3. 系统环境变量
4. PyInstaller版本 