# 智能多平台数据爬取工具 - 使用说明

## 功能概述

本工具现已完全集成GIGA和赛盈平台的数据获取功能，可以自动识别SKU类型并从相应平台获取产品信息。

## 支持平台

### GIGA平台
- **识别方式**: 包含字母的SKU或包含特殊字符的SKU
- **示例SKU**: W1239138471, DL000649AAB, SP000082AAB
- **数据来源**: 通过网页爬取获取

### 赛盈平台 (已集成API)
- **识别方式**: 8位纯数字SKU
- **示例SKU**: 32791415, 04156467, 14714836
- **数据来源**: 通过官方API获取
- **处理方式**: 单线程API调用，稳定可靠
- **功能**: 自动获取产品详情和价格信息

## 安装依赖

```bash
pip install -r requirements.txt
```

**注意**: 赛盈平台功能需要`pycryptodome`库进行加密操作，请确保已安装。

## 使用方法

1. **启动程序**
   ```bash
   python main.py
   ```

2. **配置并发设置**
   - 选择"启用多线程"或"单线程模式"
   - 设置并发线程数（建议3-5个）
   - **注意**: 并发设置仅影响GIGA平台，赛盈平台始终使用单线程API调用

3. **输入SKU信息**
   - 在文本框中输入SKU，每行一个
   - 系统会自动识别平台类型
   - 支持混合输入（GIGA和赛盈SKU可以同时输入）

4. **选择保存路径**
   - 点击"浏览"按钮选择Excel文件保存位置
   - 默认保存到桌面

5. **开始爬取**
   - 点击"开始爬取"按钮
   - 观察进度条和日志信息
   - 爬取完成后可以在数据预览区查看结果

6. **导出数据**
   - 点击"导出数据到Excel"按钮
   - 数据将保存为Excel文件

## 输出字段

工具会为每个SKU获取以下信息：

- SKU
- 产品名称
- 产品英文名称
- 毛重(kg)
- 包装尺寸(长/宽/高cm)
- 采购价
- 采购币种
- 默认供应商代码
- 申报价值(美元)
- 产品品类
- 图片URL
- 其他相关字段

## 注意事项

1. **网络连接**: 确保网络连接稳定，两个平台都需要网络访问
2. **数据库连接**: GIGA平台需要从数据库获取认证信息
3. **API限制**: 赛盈平台API有请求频率限制，工具已自动处理
4. **批量处理**: 赛盈平台SKU会批量处理，每批最多30个
5. **线程处理**: 赛盈平台使用单线程API调用，GIGA平台支持多线程
6. **错误处理**: 工具包含重试机制，会自动处理网络错误

## 常见问题

**Q: 赛盈平台SKU无法获取数据？**
A: 检查网络连接和API配置，确保pycryptodome库已安装。

**Q: GIGA平台提示认证失败？**
A: 检查数据库连接和OCSESSID配置。

**Q: 混合SKU如何处理？**
A: 工具会自动分类，先处理GIGA平台SKU，再处理赛盈平台SKU。

## 更新日志

- v1.0: 基础GIGA平台爬取功能
- v2.0: 添加赛盈平台API集成
- v2.1: 优化多线程处理和错误处理
- v2.2: 完善数据提取和格式化功能
- v2.3: 优化赛盈平台为单线程API调用模式，提高稳定性

## 技术支持

如有问题请联系开发团队。 