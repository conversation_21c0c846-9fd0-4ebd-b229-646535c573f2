import json

# 原始JSON数据
json_data = {
    "ProductInfoList": [
        {
            "Sku": "81324023",
            "PlatformGoodsCode": "C2412160024",
            "CategoryFirstCode": "A000014",
            "CategoryFirstName": "庭院和园艺",
            "CategoryFirstNameEN": "patio and gardening",
            "CategorySecondCode": "B000119",
            "CategorySecondName": "烧烤用具",
            "CategorySecondNameEN": "bbq utensils",
            "CategoryThirdCode": "C000916",
            "CategoryThirdName": "烧烤架",
            "CategoryThirdNameEN": "BBQ grill",
            "Url": "item/81324023.html",
            "CnName": "36.4*33.7*45.6cm 黑色 方形带提手披萨炉 铁制 碳烤炉",
            "EnName": "Outdoor Pizza Oven 4 in 1 Wood Fired 2-Layer Detachable Outside Ovens with Pizza Stone, Cooking Grill Grate",
            "SpecLength": 42.0,
            "SpecWidth": 39.0,
            "SpecHeight": 28.0,
            "SpecWeight": 11500.0,
            "Published": True,
            "IsClear": False,
            "FreightAttrCode": "X0018",
            "FreightAttrName": "普货",
            "PlatformCommodityCode": "C2412160024",
            "CommodityWarehouseMode": 0,
            "GoodsImageList": [
                {
                    "ImageUrl": "https://img.saleyee.cn/Resources/GoodsImages/2024/202412/202412190953108202_94ee97cd-c70c-4eb0-a59e-61834f9590fa.jpg",
                    "Sort": 0,
                    "IsMainImage": False
                },
                {
                    "ImageUrl": "https://img.saleyee.cn/Resources/GoodsImages/2024/202412/202412190953120013_67b0c1f2-33bc-4ed4-a4b7-32337ccbb99f.jpg",
                    "Sort": 1,
                    "IsMainImage": False
                },
                {
                    "ImageUrl": "https://img.saleyee.cn/Resources/GoodsImages/2024/202412/202412190953134730_838f0fce-9773-4d40-b6f6-d4b541850614.jpg",
                    "Sort": 2,
                    "IsMainImage": False
                },
                {
                    "ImageUrl": "https://img.saleyee.cn/Resources/GoodsImages/2024/202412/202412190953144803_2bcdc2eb-8893-4c8b-b4e8-c6dfbac2ac7b.jpg",
                    "Sort": 3,
                    "IsMainImage": False
                },
                {
                    "ImageUrl": "https://img.saleyee.cn/Resources/GoodsImages/2024/202412/202412190953154896_c83d8bfa-1a37-4cd2-ae06-c9098e7a2913.jpg",
                    "Sort": 4,
                    "IsMainImage": False
                },
                {
                    "ImageUrl": "https://img.saleyee.cn/Resources/GoodsImages/2024/202412/202412190953165140_6093e484-a134-4df8-a7ec-1601e2e02de5.jpg",
                    "Sort": 5,
                    "IsMainImage": False
                },
                {
                    "ImageUrl": "https://img.saleyee.cn/Resources/GoodsImages/2024/202412/202412190953175599_c07d5088-2585-4920-b8a9-31c7677c75a8.jpg",
                    "Sort": 6,
                    "IsMainImage": True
                },
                {
                    "ImageUrl": "https://img.saleyee.cn/Resources/GoodsImages/2024/202412/202412190953184735_d35323df-be0c-41b5-8213-5270fb49b2fe.jpg",
                    "Sort": 7,
                    "IsMainImage": False
                },
                {
                    "ImageUrl": "https://img.saleyee.cn/Resources/GoodsImages/2024/202412/202412190953193543_b150ec8a-2e5c-4508-aaba-b7f57d82e97f.jpg",
                    "Sort": 8,
                    "IsMainImage": False
                },
                {
                    "ImageUrl": "https://img.saleyee.cn/Resources/GoodsImages/2024/202412/202412190953202249_72785acb-bd77-4ab6-98cc-6d590c0f2b8c.jpg",
                    "Sort": 9,
                    "IsMainImage": False
                }
            ],
            "GoodsAttachmentList": [],
            "GoodsDescriptionList": [
                {
                    "Title": "Outdoor Pizza Oven 4 in 1 Wood Fired 2-Layer Detachable Outside Ovens with Pizza Stone, Cooking Grill Grate",
                    "GoodsDescriptionKeywordList": [
                        {
                            "KeyWord": "Outdoor Pizza Oven"
                        }
                    ],
                    "GoodsDescriptionLabelList": [
                        {
                            "LabelName": "英文"
                        }
                    ],
                    "GoodsDescriptionParagraphList": [
                        {
                            "ParagraphName": "首段描述",
                            "SortNo": 1,
                            "GoodsDescription": "<p><strong>Introductions:</strong><br />Transform your outdoor cooking experience with this versatile 4-in-1 Outdoor Pizza Oven. Featuring a 2-layer detachable design, this oven serves as a wood-fired pizza oven, charcoal BBQ grill, fire pit, and cooking station all in one. Perfect for family dinners, parties, and holiday gatherings, this oven offers the ultimate solution for preparing delicious pizzas, grilled meats, and even baked treats. With practical design elements like even heat circulation and a durable pizza stone, it's never been easier to achieve professional-quality results at home.</p>"
                        },
                        {
                            "ParagraphName": "特征",
                            "SortNo": 2,
                            "GoodsDescription": "<p><strong>Features:</strong><br />1. 4-in-1 Versatility: Enjoy the flexibility of this multifunctional outdoor pizza oven. Use the two layers simultaneously to bake a pizza while grilling on the bottom, or detach it for use as a standalone fire pit or charcoal grill. Perfect for pizzas, BBQ, or roasted dishes, making every outdoor gathering a culinary success.<br />2. Effortless Conversion into a Pizza Oven: Designed for ultimate versatility, this oven converts your fire pit or grill into a professional-grade pizza maker. Beyond pizzas, cook everything from subs and pretzels to chicken tenders, shrimp, and baked goods, satisfying a variety of tastes for any occasion.<br />3. Precision Cooking: Built with side vents for optimal heat circulation, this oven ensures evenly cooked pizzas every time. The cordierite pizza stone absorbs moisture from the dough, delivering a crispy crust while keeping the interior soft and fluffy.<br />4. Portable Design: Equipped with side and top handles, this oven is easy to carry and move without risk of burns. Whether fueled by wood, pellets, or charcoal, it's simple to set up and start cooking wherever your adventures take you.<br />5. Quick Assembly and Easy Use: Simple to assemble and ready to use in minutes, this pizza oven makes preparing restaurant-quality dishes effortless. A thoughtful gift for family and friends, it's ideal for anyone who loves cooking outdoors.</p>"
                        },
                        {
                            "ParagraphName": "规格",
                            "SortNo": 3,
                            "GoodsDescription": "<p><strong>Specifications:</strong><br />1. Material: Iron<br />2. Upper Oven Dimensions: (14.4 x 13.4 x 5.5) in / (36.5 x 34 x 14) cm<br />3. Lower Oven Dimensions: (13.6 x 12.8 x 11.8) in / (34.6 x 32.5 x 30) cm<br />4. Grill Grate Dimensions: (13.2 x 12.4) in / (33.5 x 31.5) cm<br />5. Handle Dimensions: Bottom Handle: 2.4 in / 6 cm; Upper Lid Handle: (4.7 x 0.7 x 1.6) in / (12 x 1.7 x 4) cm<br />6. Pizza Stone Diameter: 12 in / 30 cm<br />7. Pizza Layer Height: 3.7 in / 9.5 cm<br />8. Overall Dimensions: (14.4 x 13.3 x 18) in / (36.4 x 33.7 x 45.6) cm<br />9. Net Weight: 20.3 lbs / 9.2 kg</p>"
                        },
                        {
                            "ParagraphName": "包装内含",
                            "SortNo": 4,
                            "GoodsDescription": "<p><strong>Package Includes:</strong><br />1 x Outdoor Pizza Oven<br />1 x Set of Assembly Hardware<br />1 x Instruction Manual</p>"
                        }
                    ]
                }
            ],
            "CreateTime": "2024-12-16T09:44:38",
            "UpdateTime": "2025-05-09T09:45:26",
            "TortInfo": {
                "TortReasons": None,
                "TortTypes": None,
                "TortStatus": 2
            },
            "IsProductAuth": True,
            "ProductSiteModelList": [
                {
                    "SiteHosts": "www.saleyee.cn",
                    "WarehouseCodeList": [
                        "SZ0001"
                    ]
                }
            ],
            "BrandName": None,
            "Spu": "BLBRFMNPZB",
            "DistributionLimit": 1,
            "GoodsDistributionPlatformDetailList": [],
            "ProductAttributeList": [],
            "ProductReturnAddressList": [
                {
                    "WarehouseCode": "SZ0001",
                    "WarehouseName": "US",
                    "ThirdpartyStockCode": "EW0026",
                    "ThirdpartyStockName": "GD美东仓库",
                    "AddressCode": "AD277109",
                    "CountryCode": "US",
                    "Province": "NJ",
                    "City": "Fieldsboro",
                    "PostCode": "08505",
                    "Address1": "C/O 1170 Florence ,Gate( 102-125) Columbus Rd",
                    "ReceiveMan": "G666",
                    "Tel": "7322088878"
                }
            ],
            "ProductSendAddressList": [
                {
                    "WarehouseCode": "SZ0001",
                    "WarehouseName": "US",
                    "AddressCode": "AD277109",
                    "StockCountryCode": "US",
                    "StockCountryCnName": "美国",
                    "StockProvince": "NJ",
                    "StockCity": "Fieldsboro",
                    "StockAddress": "C/O 1170 Florence ,Gate( 102-125) Columbus Rd",
                    "StockAddressTwo": "",
                    "StockPostCode": "08505",
                    "ReceiveMan": "G666",
                    "Tel": "7322088878"
                }
            ]
        }
    ],
    "PageIndex": 1,
    "TotalCount": 0,
    "PageTotal": 0,
    "PageSize": 50
}

def extract_product_info(data):
    """
    提取产品信息中的指定字段
    """
    extracted_data = []
    
    for product in data["ProductInfoList"]:
        # 查找主图片（IsMainImage为True的图片）
        main_image_url = None
        for image in product["GoodsImageList"]:
            if image["IsMainImage"]:
                main_image_url = image["ImageUrl"]
                break
        # 组装结果
        product_info = {
            'SKU': product["Sku"],
            '产品名称':product["CnName"],    
            '产品英文名称': product["EnName"],
            '毛重(kg)': round(product["SpecWeight"] / 1000, 2),  # 重量（G）转毛重(kg)
            '包装长(cm)': product["SpecLength"],
            '包装宽(cm)': product["SpecWidth"],
            '包装高(cm)': product["SpecHeight"],
            '采购价': round(222.221, 2),
            '采购币种':"USD",
            '默认供应商代码':"赛盈GT",
            '英文申报品名':'',
            '中文申报品名':'',
            '申报价值(美元)': None,
            '产品品类(分类代码)':product["CategoryThirdName"],
            '产品销售状态':'',
            '销售负责人':'',
            '开发负责人':'',
            '附属销售员':'',
            '设计师':'',
            '运营方式':'',
            '自定义分类':'赛盈GT',
            '图片URL':main_image_url
        }
        
        extracted_data.append(product_info)
    
    return extracted_data

# 执行提取
if __name__ == "__main__":
    extracted_products = extract_product_info(json_data)
    
    print(extracted_products)