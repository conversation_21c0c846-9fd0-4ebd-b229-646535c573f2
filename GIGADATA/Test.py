# -*- coding: utf-8 -*-
"""
@Time ： 2025/7/7 17:04
@Auth ： T<PERSON><PERSON>in
@File ：Test.py
@IDE ：PyCharm
"""
import base64
import json
import uuid
from datetime import datetime, timedelta
from Crypto.Cipher import DES
from Crypto.Util.Padding import pad

from retry_request import RetryRequestBase


class GtApi:
    def __init__(self):
        self.base_url = "https://api.saleyee.cn"
        self.app_token = "ZocsKv7BS5vH3orQGxDQOQ=="
        self.app_key = "SnBIRFqo"

    def aes_encrypt(self, data, key):
        by_key = key.encode("utf-8")  # 长度需为 8
        by_iv = key.encode("utf-8")  # 与 key 相同
        # 创建 DES CBC 加密器
        cipher = DES.new(by_key, DES.MODE_CBC, iv=by_iv)
        # 使用 PKCS7 填充（块大小固定为 8 字节）
        padded_data = pad(data.encode("utf-8"), DES.block_size)
        # 加密
        encrypted_bytes = cipher.encrypt(padded_data)
        # 转 Base64
        encrypted_base64 = base64.b64encode(encrypted_bytes).decode("utf-8")
        return encrypted_base64

    def _build_params(self, json_data):
        if isinstance(json_data, (dict, list)):
            json_data = json.dumps(json_data)
        params = {
            "Version": "*******",
            "Token": self.app_token,
            "RequestId": str(uuid.uuid4()),
            "RequestTime": datetime.now().isoformat(),
            "Message": json_data,
            "Sign": None,
        }
        params["Sign"] = self.aes_encrypt(json.dumps(params, separators=(',', ':')), self.app_key)
        return params

    def _request(self, method, data, path):
        url = self.base_url + path
        try:
            json_data = self._build_params(data)
            requests = RetryRequestBase()
            response_data = requests.request(method=method, url=url, json=json_data)
            return self._parse_response(response_data)
        except requests.exceptions.RequestException as e:
            print(f"\nRequesting {url}\nMethod {method}\nBody {data} \nError: {e}")
            raise
        except json.JSONDecodeError as e:
            print(f"\nRequesting {url}\nMethod {method}\nBody {data} \nError: {e}")
            raise

    @classmethod
    def _parse_response(cls, response_data):
        if response_data["Result"] == 1:
            try:
                return json.loads(response_data["Message"])
            except json.JSONDecodeError:
                print(f"Invalid JSON data {response_data['Message']}")
                return {}
        return {}

    def get_order_list(self, start_date, end_date, page=1, **kwargs):
        data = {
            "StartTime": start_date,
            "EndTime": end_date,
            "PageIndex": page,
            **kwargs,
        }
        return self._request("POST", data, "/api/Order/QueryOrder")

    def get_product_detail(self, skus=None, spus=None, start_time=None, end_time=None, 
                          page_index=None, site=None, warehouse_code=None, **kwargs):
        """
        查询产品详情
        
        Args:
            skus (list): 商品SKU编码集合，每次最多30条
            spus (list): 商品SPU编码集合，每次最多30条  
            start_time (str): 更新开始时间，UTC时间格式，默认为当前日期
            end_time (str): 更新结束时间，UTC时间格式，默认为一周后
            page_index (int): 页码，根据更新时间查询时必须大于0，默认为1
            site (str): 站点
            warehouse_code (str): 区域编码
            **kwargs: 其他额外参数
            
        Returns:
            dict: API响应数据
            
        Note:
            四选一必填：Skus、Spus、StartTime+EndTime+PageIndex
            如果没有提供skus或spus，会自动使用时间范围查询
        """
        data = {}
        
        # 添加SKU编码
        if skus:
            if len(skus) > 30:
                raise ValueError("SKU编码集合每次最多30条")
            data["Skus"] = skus
            
        # 添加其他参数
        data.update(kwargs)

        return self._request("POST", data, "/api/Product/QueryProductDetail")

    def get_product_price(self, skus=None, spus=None, warehouse_list=None, **kwargs):
        """
        查询商品价格 (v2.0)
        
        Args:
            skus (list): 商品SKU编码集合，不能为空，不能超过30条
            spus (list): SPU编码集合，不能为空，不能超过30条
            warehouse_list (list): 区域编码集合，可多个，不填则回传所有有权限的价格
            **kwargs: 其他额外参数
            
        Returns:
            dict: API响应数据
            
        Note:
            SkuList、SpuList、StartUpdateTimeOnUtc、EndUpdateTimeOnUtc 四选一必填
        """
        data = {}
        
        # 添加SKU编码
        if skus:
            if len(skus) > 30:
                raise ValueError("SKU编码集合每次最多30条")
            data["SkuList"] = skus
            
        # 添加SPU编码
        if spus:
            if len(spus) > 30:
                raise ValueError("SPU编码集合每次最多30条")
            data["SpuList"] = spus
            
        # 添加仓库编码
        if warehouse_list:
            data["WarehouseList"] = warehouse_list
            
        # 添加其他参数
        data.update(kwargs)

        return self._request("POST", data, "/api/Product/QueryProductPriceV2")

def extract_product_info(data, api_instance=None):
    """
    提取产品信息中的指定字段
    
    Args:
        data (dict): 产品详情数据
        api_instance (GtApi): API实例，用于获取价格信息
    """
    extracted_data = []
    
    for product in data["ProductInfoList"]:
        sku = product["Sku"]
        
        # 查找主图片（IsMainImage为True的图片）
        main_image_url = None
        for image in product["GoodsImageList"]:
            if image["IsMainImage"]:
                main_image_url = image["ImageUrl"]
                break
        
        # 获取价格信息
        purchase_price = None
        declared_value = None
        selling_price = None
        original_price = None
        currency_code = None
        logistics_product_name = None
        
        if api_instance:
            try:
                print(f"正在获取SKU {sku} 的价格信息...")
                price_data = api_instance.get_product_price(skus=[sku])
                print(f"价格信息: {price_data}")
                if price_data and "DataList" in price_data:
                    data_list = price_data["DataList"]
                    if data_list and len(data_list) > 0:
                        # 取第一个SKU的价格信息
                        first_sku_data = data_list[0]
                        if "WarehousePriceList" in first_sku_data:
                            warehouse_price_list = first_sku_data["WarehousePriceList"]
                            if warehouse_price_list and len(warehouse_price_list) > 0:
                                # 查找符合条件的价格信息：CurrencyCode=USD且LogisticsProductName=Standard Shipping
                                target_price_info = None
                                for price_info in warehouse_price_list:
                                    if (price_info.get("CurrencyCode") == "USD" and 
                                        price_info.get("LogisticsProductName") == "Standard Shipping"):
                                        target_price_info = price_info
                                        break
                                
                                if target_price_info:
                                    # 获取各种价格信息
                                    selling_price = target_price_info.get("SellingPrice")
                                    original_price = target_price_info.get("OriginalPrice")
                                    currency_code = target_price_info.get("CurrencyCode")
                                    logistics_product_name = target_price_info.get("LogisticsProductName")
                                    
                                    # 使用SellingPrice作为采购价
                                    if selling_price:
                                        purchase_price = round(selling_price, 2)
                                        declared_value = round(selling_price * 0.6, 2)  # 申报价值为售价的60%
                                        print(f"获取到SKU {sku} 的价格信息 (USD + Standard Shipping):")
                                        print(f"  - 售价(SellingPrice): ${selling_price} {currency_code}")
                                        print(f"  - 原价(OriginalPrice): ${original_price} {currency_code}")
                                        print(f"  - 物流产品: {logistics_product_name}")
                                    else:
                                        print(f"符合条件的价格数据中未找到SellingPrice字段")
                                else:
                                    print(f"未找到SKU {sku} 符合条件的价格信息 (CurrencyCode=USD且LogisticsProductName=Standard Shipping)")
                                    print(f"可用的价格选项:")
                                    for i, price_info in enumerate(warehouse_price_list):
                                        print(f"  选项{i+1}: {price_info.get('CurrencyCode')} - {price_info.get('LogisticsProductName')}")
                            else:
                                print(f"未找到SKU {sku} 的仓库价格信息")
                        else:
                            print(f"SKU {sku} 数据中未找到WarehousePriceList字段")
                    else:
                        print(f"未找到SKU {sku} 的价格信息")
                else:
                    print(f"价格API返回数据格式异常: {price_data}")
            except Exception as e:
                print(f"获取SKU {sku} 价格时发生错误: {e}")
        
        # 组装结果
        product_info = {
            'SKU': sku,
            '产品名称':product["CnName"],    
            '产品英文名称': product["EnName"],
            '毛重(kg)': round(product["SpecWeight"] / 1000, 2),  # 重量（G）转毛重(kg)
            '包装长(cm)': product["SpecLength"],
            '包装宽(cm)': product["SpecWidth"],
            '包装高(cm)': product["SpecHeight"],
            '采购价': purchase_price,
            '采购币种': currency_code or "USD",
            '默认供应商代码':"赛盈GT",
            '英文申报品名':'',
            '中文申报品名':'',
            '申报价值(美元)': declared_value,
            '产品品类(分类代码)':product["CategoryThirdName"],
            '产品销售状态':'',
            '销售负责人':'',
            '开发负责人':'',
            '附属销售员':'',
            '设计师':'',
            '运营方式':'',
            '自定义分类':'赛盈GT',
            '图片URL':main_image_url
        }
        
        extracted_data.append(product_info)
    
    return extracted_data

# 使用示例
if __name__ == "__main__":
    # 创建API实例
    api = GtApi()

    try:
        print("\n2. 使用默认时间范围查询产品详情...")
        products_default_time = api.get_product_detail(skus=['04156467'])
        print(f"API返回数据: {products_default_time}")
        
        if products_default_time:
            extracted_products = extract_product_info(products_default_time, api)
            print(f"提取的产品信息: {extracted_products}")
        else:
            print("API返回数据为空")
    except Exception as e:
        print(f"默认时间范围查询产品详情时发生错误: {e}")
        import traceback
        traceback.print_exc()
    print("\n========== API使用示例完成 ==========")
    
