import json as jsonp
import time
import requests
from requests.adapters import HTTPAdapter
from urllib3.util import Retry




class RetryRequestBase:
    def __init__(self, headers=None, retries=5, backoff_factor=1, status_forcelist=None):
        """
        初始化基础类
        :param headers: 请求头
        :param retries: 最大重试次数
        :param backoff_factor: 重试间隔时间因子
        :param status_forcelist: 触发重试的状态码列表
        """
        self.headers = headers or {}
        self.retries = retries
        self.backoff_factor = backoff_factor
        self.status_forcelist = status_forcelist or [500, 502, 503, 504]

        # 创建 Session 并配置重试策略
        self.session = requests.Session()
        retry_strategy = Retry(
            total=self.retries,
            backoff_factor=self.backoff_factor,
            status_forcelist=self.status_forcelist,
            allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS"],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=100, pool_maxsize=100)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

    def request(self, method, url, params=None, data=None, json=None, timeout=(3.05, 30), **kwargs):
        """
        发送 HTTP 请求
        :param method: 请求方法（GET, POST, PUT, DELETE 等）
        :param url: 请求 URL
        :param params: 查询参数
        :param data: 表单数据
        :param json: JSON 数据
        :param kwargs: 其他 requests 参数
        :return: 响应对象
        """
        debug_dict = {
            "headers": self.headers,
            "url": url,
            "method": method,
            "params": params,
            "data": data,
            "json": json,
        }
        try:
            response = self.session.request(
                method=method,
                url=url,
                headers=self.headers,
                params=params,
                data=data,
                json=json,
                timeout=timeout,
                **kwargs,
            )
            if response.status_code == 429:
                retry_after = int(response.headers.get("Retry-After", 60))
                time.sleep(retry_after)
                return self.session.request(
                    method=method,
                    url=url,
                    headers=self.headers,
                    params=params,
                    data=data,
                    json=json,
                    timeout=timeout,
                    **kwargs,
                )

            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"{jsonp.dumps(debug_dict, ensure_ascii=False, indent=2)}\n响应状态码: {response.status_code}")
            raise

    def get(self, url, params=None, **kwargs):
        """发送 GET 请求"""
        return self.request("GET", url, params=params, **kwargs)

    def post(self, url, data=None, json=None, **kwargs):
        """发送 POST 请求"""
        return self.request("POST", url, data=data, json=json, **kwargs)

    def put(self, url, data=None, json=None, **kwargs):
        """发送 PUT 请求"""
        return self.request("PUT", url, data=data, json=json, **kwargs)

    def delete(self, url, **kwargs):
        """发送 DELETE 请求"""
        return self.request("DELETE", url, **kwargs)
