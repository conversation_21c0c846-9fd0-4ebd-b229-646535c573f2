# 网页数据爬取工具

这是一个基于PyQt5的网页数据爬取工具，支持批量处理SKU信息，具有友好的图形用户界面。专门针对GIGAB2B网站进行数据采集。

## 功能特点

- ✅ 直观的图形用户界面
- ✅ 批量SKU信息处理，无数量限制
- ✅ 实时进度条显示爬取进度
- ✅ 详细的爬取日志
- ✅ 数据预览功能
- ✅ Excel文件导出功能
- ✅ 可配置请求延时
- ✅ 支持停止正在进行的爬取任务
- ✅ 自动单位转换（英寸→厘米，磅→千克）
- ✅ 网络请求自动重试机制，提高稳定性

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行程序

```bash
python main.py
```

## 使用说明

1. **输入SKU信息**: 在"SKU信息输入"框中输入要处理的SKU，每行一个
2. **选择保存路径**: 点击"浏览"按钮选择Excel文件保存位置
3. **开始爬取**: 点击"开始爬取"按钮开始数据采集
4. **查看进度**: 实时查看进度条和日志信息
5. **预览数据**: 爬取完成后在"数据预览"区域查看结果
6. **保存数据**: 点击"保存数据"按钮将结果导出到Excel

## 数据提取说明

程序会为每个SKU执行以下操作：
1. 构建搜索URL并发送POST请求到GIGAB2B网站
2. 从搜索结果中获取产品ID
3. 使用产品ID获取详细信息和价格
4. 提取并处理以下数据：
   - 产品SKU和名称
   - 包装尺寸（长宽高）和重量（自动转换为公制单位）
   - 价格信息
   - 产品图片URL

提取的数据包括：
- SKU
- 产品英文名称
- 毛重(kg)
- 包装尺寸(cm)
- 采购价格(USD)
- 申报价值
- 产品图片URL
- 其他附加信息

## 错误处理与重试机制

程序实现了完善的错误处理和请求重试机制：
- 每个网络请求最多尝试3次
- 请求失败后等待2秒再重试
- 详细的错误日志记录
- 针对不同类型错误（网络错误、解析错误等）的专门处理

这些机制大大提高了程序在网络不稳定情况下的可靠性。

## 自定义数据提取

要修改数据提取逻辑，请编辑 `main.py` 文件中的 `extract_data` 方法。
要修改搜索URL构建方式，请编辑 `start_scraping` 方法中的 `base_url` 变量。

## 注意事项

- 请遵守网站的robots.txt规则
- 设置适当的延时避免对目标网站造成压力
- 某些网站可能有反爬虫措施，请合理使用
- 建议先用少量SKU进行测试
- 本工具仅用于学习和研究，请勿用于商业用途 