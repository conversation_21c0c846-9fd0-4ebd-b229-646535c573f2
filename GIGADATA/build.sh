#!/bin/bash

echo "开始打包网页数据爬取工具..."
echo

# 检查是否安装了所需依赖
echo "正在检查依赖..."
pip install -r requirements.txt
echo

# 清理之前的打包文件
echo "清理之前的打包文件..."
rm -rf dist build
echo

# 开始打包
echo "开始打包程序..."
pyinstaller build.spec
echo

# 检查打包结果
if [ -f "dist/网页数据爬取工具" ]; then
    echo "打包成功！"
    echo "可执行文件位置: dist/网页数据爬取工具"
    echo
    echo "您可以将 dist 文件夹中的所有文件复制到目标电脑上运行。"
else
    echo "打包失败，请检查错误信息。"
fi

read -p "按回车键继续..." 