# -*- coding: utf-8 -*-
"""
多线程功能测试脚本
用于验证SKU分类和多线程处理逻辑
"""

# 测试SKU分类功能
def test_sku_classification():
    print("=== 测试SKU分类功能 ===")
    
    # 模拟SKU列表
    test_skus = [
        "W1239138471",  # GIGA - 包含字母
        "32791415",     # 赛盈 - 8位数字
        "DL000649AAB",  # GIGA - 包含字母
        "04156467",     # 赛盈 - 8位数字
        "SP000082AAB",  # GIGA - 包含字母
        "14714836",     # 赛盈 - 8位数字
        "W219106473",   # GIGA - 包含字母
        "54672.00BSKGRY", # GIGA - 包含特殊字符
        "123456789",    # 未知格式 - 9位数字
        "ABC123"        # GIGA - 包含字母
    ]
    
    # 分类逻辑
    def is_giga_sku(sku):
        import re
        return bool(re.search(r'[A-Za-z]', sku)) or '.' in sku
    
    def is_saiyii_sku(sku):
        return sku.isdigit() and len(sku) == 8
    
    giga_skus = []
    saiyii_skus = []
    unknown_skus = []
    
    for sku in test_skus:
        if is_giga_sku(sku):
            giga_skus.append(sku)
        elif is_saiyii_sku(sku):
            saiyii_skus.append(sku)
        else:
            unknown_skus.append(sku)
    
    print(f"GIGA平台SKU ({len(giga_skus)}个):")
    for sku in giga_skus:
        print(f"  - {sku}")
    
    print(f"\n赛盈平台SKU ({len(saiyii_skus)}个):")
    for sku in saiyii_skus:
        print(f"  - {sku}")
    
    print(f"\n未识别SKU ({len(unknown_skus)}个):")
    for sku in unknown_skus:
        print(f"  - {sku}")
    
    print(f"\n总计: {len(test_skus)} 个SKU")
    return giga_skus, saiyii_skus, unknown_skus

# 测试多线程处理模拟
def test_multithread_simulation():
    print("\n=== 测试多线程处理模拟 ===")
    
    import time
    import threading
    from concurrent.futures import ThreadPoolExecutor, as_completed
    
    def simulate_fetch_data(sku):
        """模拟数据获取"""
        # 模拟网络请求延迟
        delay = 0.5 + (hash(sku) % 10) * 0.1  # 0.5-1.4秒的随机延迟
        time.sleep(delay)
        
        # 模拟数据返回
        return {
            'sku': sku,
            'name': f'Product_{sku}',
            'price': 10.0 + (hash(sku) % 100),
            'processing_time': delay
        }
    
    giga_skus = ["W1239138471", "DL000649AAB", "SP000082AAB", "W219106473", "54672.00BSKGRY"]
    max_workers = 3
    
    print(f"处理 {len(giga_skus)} 个GIGA平台SKU，使用 {max_workers} 个线程")
    
    # 单线程处理时间测试
    print("\n单线程处理:")
    start_time = time.time()
    single_thread_results = []
    for sku in giga_skus:
        result = simulate_fetch_data(sku)
        single_thread_results.append(result)
        print(f"  完成: {sku} (用时: {result['processing_time']:.2f}秒)")
    single_thread_total = time.time() - start_time
    print(f"单线程总用时: {single_thread_total:.2f}秒")
    
    # 多线程处理时间测试
    print("\n多线程处理:")
    start_time = time.time()
    multi_thread_results = []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_sku = {executor.submit(simulate_fetch_data, sku): sku for sku in giga_skus}
        
        # 收集结果
        for future in as_completed(future_to_sku):
            sku = future_to_sku[future]
            result = future.result()
            multi_thread_results.append(result)
            print(f"  完成: {sku} (用时: {result['processing_time']:.2f}秒)")
    
    multi_thread_total = time.time() - start_time
    print(f"多线程总用时: {multi_thread_total:.2f}秒")
    
    # 性能对比
    improvement = ((single_thread_total - multi_thread_total) / single_thread_total) * 100
    print(f"\n性能提升: {improvement:.1f}%")
    
    return single_thread_results, multi_thread_results

if __name__ == "__main__":
    # 运行测试
    giga_skus, saiyii_skus, unknown_skus = test_sku_classification()
    single_results, multi_results = test_multithread_simulation()
    
    print("\n=== 测试完成 ===")
    print("多线程功能测试通过！") 