import json
import sys
import os
import time
import requests
import urllib.parse
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 修复numpy CPU dispatcher初始化问题
os.environ['OPENBLAS_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
os.environ['NUMPY_EXPERIMENTAL_ARRAY_FUNCTION'] = '0'

# 在导入numpy之前设置环境变量
import multiprocessing
try:
    multiprocessing.set_start_method('spawn', force=True)
except RuntimeError:
    # 如果已经设置过就忽略
    pass

# 修复numpy/pandas导入问题
try:
    import numpy as np
    # 禁用numpy的多线程以避免冲突
    np.seterr(all='ignore')
    import pandas as pd
    import pymysql
except ImportError as e:
    print(f"导入错误: {e}")
    # 如果导入失败，尝试重新导入
    try:
        import numpy as np
        np.seterr(all='ignore')
        import pandas as pd
        import pymysql
    except ImportError:
        print("无法导入必要的数据处理库")

# 设置 PyQt5 环境变量
import PyQt5
pyqt_path = os.path.dirname(PyQt5.__file__)
os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = os.path.join(pyqt_path, 'Qt5', 'plugins', 'platforms')
os.environ['QT_PLUGIN_PATH'] = os.path.join(pyqt_path, 'Qt5', 'plugins')

# 添加 DLL 路径
qt_bin_path = os.path.join(pyqt_path, 'Qt5', 'bin')
if os.path.exists(qt_bin_path):
    os.environ['PATH'] = qt_bin_path + os.pathsep + os.environ.get('PATH', '')
from bs4 import BeautifulSoup
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QPushButton, QLineEdit, QTextEdit, QProgressBar,
                            QLabel, QFileDialog, QGroupBox, QGridLayout, QSpinBox,
                            QMessageBox, QComboBox, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView, QSplitter, QFrame,
                            QTabWidget, QScrollArea, QToolTip, QStatusBar, QDialog,
                            QFormLayout, QDialogButtonBox, QCheckBox)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, QMutex, Qt, QPropertyAnimation, QEasingCurve, QSettings
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QPixmap, QPainter


class ConfigDialog(QDialog):
    """配置对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("⚙️ 系统配置")
        self.setModal(True)
        self.setFixedSize(650, 700)  # 增加窗口大小以适应新设计
        self.settings = QSettings("DataScrapingTool", "Config")
        self.init_ui()
        self.load_settings()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("🔧 系统配置设置")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background: #f8f9fa;
                border: 1px solid #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom-color: white;
                color: #2c3e50;
            }
        """)

        # 赛盈平台配置标签页
        saiyii_tab = self.create_saiyii_config_tab()
        self.tab_widget.addTab(saiyii_tab, "🏪 赛盈平台")

        # GIGA平台配置标签页
        giga_tab = self.create_giga_config_tab()
        self.tab_widget.addTab(giga_tab, "📦 GIGA平台")

        # 通用设置标签页
        general_tab = self.create_general_config_tab()
        self.tab_widget.addTab(general_tab, "⚙️ 通用设置")

        layout.addWidget(self.tab_widget)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # 重置按钮
        reset_button = QPushButton("🔄 重置默认")
        reset_button.clicked.connect(self.reset_to_defaults)
        reset_button.setStyleSheet(self.get_button_style("#e67e22"))
        button_layout.addWidget(reset_button)

        # 测试连接按钮
        test_button = QPushButton("🔍 测试连接")
        test_button.clicked.connect(self.test_connection)
        test_button.setStyleSheet(self.get_button_style("#3498db"))
        button_layout.addWidget(test_button)

        # 标准按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.setStyleSheet("""
            QPushButton {
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 6px;
                min-width: 80px;
            }
        """)
        button_layout.addWidget(button_box)

        layout.addLayout(button_layout)

    def create_saiyii_config_tab(self):
        """创建赛盈平台配置标签页 - 使用GIGA样式"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 说明文本
        info_label = QLabel("🏪 赛盈平台API配置")
        info_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        info_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(info_label)

        desc_label = QLabel("配置赛盈平台的API访问参数，用于获取产品数据")
        desc_label.setStyleSheet("color: #7f8c8d; margin-bottom: 15px;")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        # 配置表单
        form_layout = QFormLayout()
        form_layout.setSpacing(12)

        # saiyii_base_url
        self.saiyii_base_url_input = QLineEdit()
        self.saiyii_base_url_input.setPlaceholderText("https://api.saleyee.com")
        self.saiyii_base_url_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("🌐 saiyii_base_url:", self.saiyii_base_url_input)

        # saiyii_app_token
        self.saiyii_app_token_input = QLineEdit()
        self.saiyii_app_token_input.setPlaceholderText("ZocsKv7BS5vH3orQGxDQOQ==")
        self.saiyii_app_token_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("🔑 saiyii_app_token:", self.saiyii_app_token_input)

        # saiyii_app_key
        self.saiyii_app_key_input = QLineEdit()
        self.saiyii_app_key_input.setPlaceholderText("SnBIRFqo")
        self.saiyii_app_key_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("🗝️ saiyii_app_key:", self.saiyii_app_key_input)

        layout.addLayout(form_layout)

        # 帮助信息
        help_frame = QFrame()
        help_frame.setStyleSheet("QFrame { background: #f8f9fa; border-radius: 6px; padding: 10px; }")
        help_layout = QVBoxLayout(help_frame)

        help_title = QLabel("💡 获取API参数的方法:")
        help_title.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        help_title.setStyleSheet("color: #495057;")
        help_layout.addWidget(help_title)

        help_text = QLabel("""
1. 登录赛盈平台开发者中心
2. 进入API管理页面
3. 创建或查看现有应用
4. 复制App Token和App Key
5. 将参数填入上方输入框
        """.strip())
        help_text.setStyleSheet("color: #6c757d; font-size: 10px; line-height: 1.4;")
        help_text.setWordWrap(True)
        help_layout.addWidget(help_text)

        layout.addWidget(help_frame)
        layout.addStretch()

        return widget




    def create_giga_config_tab(self):
        """创建GIGA平台配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 说明文本
        info_label = QLabel("📦 GIGA平台配置")
        info_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        info_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(info_label)

        desc_label = QLabel("配置GIGA平台的访问参数和爬取设置")
        desc_label.setStyleSheet("color: #7f8c8d; margin-bottom: 15px;")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        # 配置表单
        form_layout = QFormLayout()
        form_layout.setSpacing(12)

        # 基础URL
        self.giga_base_url_input = QLineEdit()
        self.giga_base_url_input.setPlaceholderText("https://www.gigab2b.com/index.php?route=/product/list/search")
        self.giga_base_url_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("🌐 基础URL:", self.giga_base_url_input)

        # 请求延迟
        self.giga_delay_input = QSpinBox()
        self.giga_delay_input.setRange(1, 10)
        self.giga_delay_input.setValue(2)
        self.giga_delay_input.setSuffix(" 秒")
        self.giga_delay_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("⏱️ 请求延迟:", self.giga_delay_input)

        # 最大重试次数
        self.giga_max_retries_input = QSpinBox()
        self.giga_max_retries_input.setRange(1, 10)
        self.giga_max_retries_input.setValue(3)
        self.giga_max_retries_input.setSuffix(" 次")
        self.giga_max_retries_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("🔄 最大重试:", self.giga_max_retries_input)

        layout.addLayout(form_layout)

        # 高级设置
        advanced_frame = QFrame()
        advanced_frame.setStyleSheet("QFrame { background: #f8f9fa; border-radius: 6px; padding: 10px; }")
        advanced_layout = QVBoxLayout(advanced_frame)

        advanced_title = QLabel("🔧 高级设置:")
        advanced_title.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        advanced_title.setStyleSheet("color: #495057;")
        advanced_layout.addWidget(advanced_title)

        # 启用调试模式
        self.giga_debug_checkbox = QCheckBox("启用调试模式（显示详细日志）")
        self.giga_debug_checkbox.setStyleSheet("color: #6c757d;")
        advanced_layout.addWidget(self.giga_debug_checkbox)

        # 启用缓存
        self.giga_cache_checkbox = QCheckBox("启用结果缓存（避免重复请求）")
        self.giga_cache_checkbox.setStyleSheet("color: #6c757d;")
        advanced_layout.addWidget(self.giga_cache_checkbox)

        layout.addWidget(advanced_frame)
        layout.addStretch()

        return widget

    def create_general_config_tab(self):
        """创建通用设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 说明文本
        info_label = QLabel("⚙️ 通用设置")
        info_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        info_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(info_label)

        desc_label = QLabel("配置应用程序的通用参数和行为设置")
        desc_label.setStyleSheet("color: #7f8c8d; margin-bottom: 15px;")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        # 配置表单
        form_layout = QFormLayout()
        form_layout.setSpacing(12)

        # 默认保存路径
        path_layout = QHBoxLayout()
        self.default_save_path_input = QLineEdit()
        self.default_save_path_input.setPlaceholderText("选择默认保存路径...")
        self.default_save_path_input.setStyleSheet(self.get_input_style())
        path_layout.addWidget(self.default_save_path_input)

        browse_path_button = QPushButton("📁")
        browse_path_button.clicked.connect(self.browse_default_path)
        browse_path_button.setStyleSheet(self.get_button_style("#e67e22"))
        browse_path_button.setMaximumWidth(40)
        path_layout.addWidget(browse_path_button)

        form_layout.addRow("💾 默认保存路径:", path_layout)

        # 自动保存间隔
        self.auto_save_interval_input = QSpinBox()
        self.auto_save_interval_input.setRange(0, 60)
        self.auto_save_interval_input.setValue(0)
        self.auto_save_interval_input.setSuffix(" 分钟 (0=禁用)")
        self.auto_save_interval_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("💾 自动保存间隔:", self.auto_save_interval_input)

        layout.addLayout(form_layout)

        # 界面设置
        ui_frame = QFrame()
        ui_frame.setStyleSheet("QFrame { background: #f8f9fa; border-radius: 6px; padding: 10px; }")
        ui_layout = QVBoxLayout(ui_frame)

        ui_title = QLabel("🎨 界面设置:")
        ui_title.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        ui_title.setStyleSheet("color: #495057;")
        ui_layout.addWidget(ui_title)

        # 启动时显示欢迎信息
        self.show_welcome_checkbox = QCheckBox("启动时显示欢迎信息")
        self.show_welcome_checkbox.setChecked(True)
        self.show_welcome_checkbox.setStyleSheet("color: #6c757d;")
        ui_layout.addWidget(self.show_welcome_checkbox)

        # 自动滚动日志
        self.auto_scroll_log_checkbox = QCheckBox("自动滚动到最新日志")
        self.auto_scroll_log_checkbox.setChecked(True)
        self.auto_scroll_log_checkbox.setStyleSheet("color: #6c757d;")
        ui_layout.addWidget(self.auto_scroll_log_checkbox)

        # 记住窗口大小
        self.remember_window_size_checkbox = QCheckBox("记住窗口大小和位置")
        self.remember_window_size_checkbox.setChecked(True)
        self.remember_window_size_checkbox.setStyleSheet("color: #6c757d;")
        ui_layout.addWidget(self.remember_window_size_checkbox)

        layout.addWidget(ui_frame)
        layout.addStretch()

        return widget

    def get_input_style(self):
        """获取输入框样式"""
        return """
            QLineEdit, QSpinBox {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px 12px;
                background-color: white;
                font-size: 11px;
                selection-background-color: #3498db;
            }
            QLineEdit:focus, QSpinBox:focus {
                border-color: #3498db;
            }
        """

    def get_clear_input_style(self):
        """获取清晰可读的输入框样式"""
        return """
            QLineEdit {
                border: 3px solid #7f8c8d;
                border-radius: 8px;
                padding: 12px 16px;
                background-color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
                color: #000000;
                selection-background-color: #3498db;
                selection-color: white;
            }
            QLineEdit:focus {
                border-color: #2980b9;
                background-color: #f8f9fa;
                color: #000000;
            }
            QLineEdit:hover {
                border-color: #34495e;
                background-color: #f8f9fa;
            }
            QLineEdit::placeholder {
                color: #95a5a6;
                font-style: italic;
                font-weight: normal;
            }
        """

    def get_button_style(self, color="#3498db"):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background-color: {color};
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 11px;
                font-weight: bold;
                border-radius: 6px;
                min-width: 60px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """

    def darken_color(self, color, factor=0.9):
        """使颜色变暗"""
        if color.startswith('#'):
            color = color[1:]

        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)

        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)

        return f"#{r:02x}{g:02x}{b:02x}"

    def browse_default_path(self):
        """浏览默认保存路径"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "选择默认保存路径",
            self.default_save_path_input.text() or "scraped_data.xlsx",
            "Excel文件 (*.xlsx);;所有文件 (*)"
        )
        if file_path:
            self.default_save_path_input.setText(file_path)

    def load_settings(self):
        """加载设置"""
        # 赛盈平台设置
        self.saiyii_base_url_input.setText(
            self.settings.value("saiyii/base_url", "https://api.saleyee.com")
        )
        self.saiyii_app_token_input.setText(
            self.settings.value("saiyii/app_token", "ZocsKv7BS5vH3orQGxDQOQ==")
        )
        self.saiyii_app_key_input.setText(
            self.settings.value("saiyii/app_key", "SnBIRFqo")
        )

        # GIGA平台设置
        self.giga_base_url_input.setText(
            self.settings.value("giga/base_url", "https://www.gigab2b.com/index.php?route=/product/list/search")
        )
        self.giga_delay_input.setValue(
            int(self.settings.value("giga/delay", 2))
        )
        self.giga_max_retries_input.setValue(
            int(self.settings.value("giga/max_retries", 3))
        )
        self.giga_debug_checkbox.setChecked(
            self.settings.value("giga/debug", False, type=bool)
        )
        self.giga_cache_checkbox.setChecked(
            self.settings.value("giga/cache", True, type=bool)
        )

        # 通用设置
        default_path = os.path.join(os.path.expanduser("~"), "Desktop", "scraped_data.xlsx")
        self.default_save_path_input.setText(
            self.settings.value("general/default_save_path", default_path)
        )
        self.auto_save_interval_input.setValue(
            int(self.settings.value("general/auto_save_interval", 0))
        )
        self.show_welcome_checkbox.setChecked(
            self.settings.value("general/show_welcome", True, type=bool)
        )
        self.auto_scroll_log_checkbox.setChecked(
            self.settings.value("general/auto_scroll_log", True, type=bool)
        )
        self.remember_window_size_checkbox.setChecked(
            self.settings.value("general/remember_window_size", True, type=bool)
        )

    def save_settings(self):
        """保存设置"""
        # 赛盈平台设置
        self.settings.setValue("saiyii/base_url", self.saiyii_base_url_input.text())
        self.settings.setValue("saiyii/app_token", self.saiyii_app_token_input.text())
        self.settings.setValue("saiyii/app_key", self.saiyii_app_key_input.text())

        # GIGA平台设置
        self.settings.setValue("giga/base_url", self.giga_base_url_input.text())
        self.settings.setValue("giga/delay", self.giga_delay_input.value())
        self.settings.setValue("giga/max_retries", self.giga_max_retries_input.value())
        self.settings.setValue("giga/debug", self.giga_debug_checkbox.isChecked())
        self.settings.setValue("giga/cache", self.giga_cache_checkbox.isChecked())

        # 通用设置
        self.settings.setValue("general/default_save_path", self.default_save_path_input.text())
        self.settings.setValue("general/auto_save_interval", self.auto_save_interval_input.value())
        self.settings.setValue("general/show_welcome", self.show_welcome_checkbox.isChecked())
        self.settings.setValue("general/auto_scroll_log", self.auto_scroll_log_checkbox.isChecked())
        self.settings.setValue("general/remember_window_size", self.remember_window_size_checkbox.isChecked())

    def reset_to_defaults(self):
        """重置为默认设置"""
        reply = QMessageBox.question(
            self,
            "确认重置",
            "确定要重置所有设置为默认值吗？\n这将清除所有自定义配置。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.settings.clear()
            self.load_settings()
            QMessageBox.information(self, "重置完成", "所有设置已重置为默认值")

    def test_connection(self):
        """测试连接"""
        try:
            import requests
            base_url = self.saiyii_base_url_input.text().strip()
            app_token = self.saiyii_app_token_input.text().strip()
            app_key = self.saiyii_app_key_input.text().strip()

            if not base_url:
                QMessageBox.warning(self, "配置检查", "⚠️ 请填写API基础地址")
                return

            if not app_token:
                QMessageBox.warning(self, "配置检查", "⚠️ 请填写App Token")
                return

            if not app_key:
                QMessageBox.warning(self, "配置检查", "⚠️ 请填写App Key")
                return

            # 简单的连接测试
            QMessageBox.information(self, "连接测试", "🔍 正在测试连接...")

            response = requests.get(base_url, timeout=5)
            if response.status_code in [200, 404, 405]:  # 这些状态码都表示服务器可达
                QMessageBox.information(self, "连接测试", "✅ 赛盈平台连接正常！\n服务器响应正常")
            else:
                QMessageBox.warning(self, "连接测试", f"⚠️ 服务器响应异常\n状态码: {response.status_code}")

        except requests.exceptions.Timeout:
            QMessageBox.critical(self, "连接测试", "❌ 连接超时\n请检查网络连接或API地址")
        except requests.exceptions.ConnectionError:
            QMessageBox.critical(self, "连接测试", "❌ 无法连接到服务器\n请检查API地址是否正确")
        except ImportError:
            QMessageBox.critical(self, "连接测试", "❌ 缺少requests模块\n请安装: pip install requests")
        except Exception as e:
            QMessageBox.critical(self, "连接测试", f"❌ 连接测试失败\n错误信息: {str(e)}")

    def accept(self):
        """确认并保存设置"""
        self.save_settings()
        super().accept()

    def get_config(self):
        """获取配置信息"""
        return {
            'saiyii': {
                'base_url': self.saiyii_base_url_input.text(),
                'app_token': self.saiyii_app_token_input.text(),
                'app_key': self.saiyii_app_key_input.text()
            },
            'giga': {
                'base_url': self.giga_base_url_input.text(),
                'delay': self.giga_delay_input.value(),
                'max_retries': self.giga_max_retries_input.value(),
                'debug': self.giga_debug_checkbox.isChecked(),
                'cache': self.giga_cache_checkbox.isChecked()
            },
            'general': {
                'default_save_path': self.default_save_path_input.text(),
                'auto_save_interval': self.auto_save_interval_input.value(),
                'show_welcome': self.show_welcome_checkbox.isChecked(),
                'auto_scroll_log': self.auto_scroll_log_checkbox.isChecked(),
                'remember_window_size': self.remember_window_size_checkbox.isChecked()
            }
        }


class WebScraperThread(QThread):
    # 信号定义
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str)
    scraping_finished = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, url, delay=1.5, max_workers=3):
        super().__init__()
        self.url = url
        self.delay = delay
        self.max_workers = max_workers  # 最大并发线程数
        self.use_multithread = True  # 是否使用多线程，默认开启
        self.is_running = True
        self.sku_list = []
        self.ocsessid = None  # 存储从数据库获取的OCSESSID

        # 线程安全相关
        self.mutex = QMutex()
        self.processed_skus = 0
        self.total_skus = 0

        # 配置参数（将由主程序设置）
        self.saiyii_base_url = "https://api.saleyee.com"
        self.saiyii_app_token = "ZocsKv7BS5vH3orQGxDQOQ=="
        self.saiyii_app_key = "SnBIRFqo"
        self.giga_max_retries = 3
        self.giga_debug = False
    
    def aes_encrypt(self, data, key):
        """DES加密方法用于赛盈平台API签名"""
        try:
            import base64
            from Crypto.Cipher import DES
            from Crypto.Util.Padding import pad
            
            by_key = key.encode("utf-8")  # 长度需为 8
            by_iv = key.encode("utf-8")  # 与 key 相同
            # 创建 DES CBC 加密器
            cipher = DES.new(by_key, DES.MODE_CBC, iv=by_iv)
            # 使用 PKCS7 填充（块大小固定为 8 字节）
            padded_data = pad(data.encode("utf-8"), DES.block_size)
            # 加密
            encrypted_bytes = cipher.encrypt(padded_data)
            # 转 Base64
            encrypted_base64 = base64.b64encode(encrypted_bytes).decode("utf-8")
            return encrypted_base64
        except ImportError:
            self.thread_safe_log("缺少加密库，无法使用赛盈平台功能")
            return None
        except Exception as e:
            self.thread_safe_log(f"加密错误: {str(e)}")
            return None
    
    def _build_saiyii_params(self, json_data):
        """构建赛盈平台API请求参数"""
        import uuid
        from datetime import datetime
        
        if isinstance(json_data, (dict, list)):
            json_data = json.dumps(json_data)
        
        params = {
            "Version": "*******",
            "Token": self.saiyii_app_token,
            "RequestId": str(uuid.uuid4()),
            "RequestTime": datetime.now().isoformat(),
            "Message": json_data,
            "Sign": None,
        }
        
        sign_data = self.aes_encrypt(json.dumps(params, separators=(',', ':')), self.saiyii_app_key)
        if sign_data:
            params["Sign"] = sign_data
        
        return params
    
    def _parse_saiyii_response(self, response_data):
        """解析赛盈平台API响应"""
        self.thread_safe_log(f"🔍 开始解析API响应...")
        
        if not response_data:
            self.thread_safe_log("❌ 响应数据为空")
            return {}
        
        result = response_data.get("Result")
        
        if result == 1:
            try:
                message = response_data.get("Message", "")
                
                parsed_message = json.loads(message)
                self.thread_safe_log(f"✅ Message解析成功")
                return parsed_message
                
            except json.JSONDecodeError as e:
                self.thread_safe_log(f"❌ Message JSON解析失败: {str(e)}")
                self.thread_safe_log(f"📄 原始Message内容: {response_data.get('Message', '')}")
                return {}
        else:
            self.thread_safe_log(f"❌ API返回失败，Result={result}")
            self.thread_safe_log(f"📄 错误信息: {response_data.get('Message', '无错误信息')}")
            return {}
    
    def _saiyii_request(self, method, data, path):
        """发送赛盈平台API请求"""
        url = self.saiyii_base_url + path
        max_retries = 3  # 最大重试次数
        
        # 判断是否为价格API，使用不同的重试策略
        is_price_api = "QueryProductPriceV2" in path
        
        self.thread_safe_log(f"🔗 准备请求赛盈API: {url}")
        self.thread_safe_log(f"📤 请求数据: {data}")
        
        if is_price_api:
            self.thread_safe_log("💰 检测到价格API，使用快速重试策略")
        
        json_data = self._build_saiyii_params(data)
        if not json_data.get("Sign"):
            self.thread_safe_log("❌ 签名生成失败，无法发送请求")
            return {}
        
        self.thread_safe_log("✅ 签名生成成功，开始重试请求...")
        
        for attempt in range(max_retries):
            try:
                self.thread_safe_log(f"🌐 赛盈API尝试 {attempt + 1}/{max_retries}: {method} {url}")
                
                # 为价格API使用更短的超时时间
                timeout = 15 if is_price_api else 30
                
                response = requests.request(
                    method=method, 
                    url=url, 
                    json=json_data,
                    timeout=timeout,
                    headers={
                        'Content-Type': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                )
                
                self.thread_safe_log(f"📥 赛盈API响应状态码: {response.status_code}")
                
                # 检查HTTP状态码
                if response.status_code != 200:
                    self.thread_safe_log(f"❌ HTTP状态码错误: {response.status_code}")
                    self.thread_safe_log(f"📄 响应内容: {response.text[:500]}...")
                    
                    if attempt < max_retries - 1:
                        delay = self._get_saiyii_retry_delay(attempt, "http_error", is_price_api)
                        self.thread_safe_log(f"⏰ HTTP错误等待 {delay} 秒后重试...")
                        time.sleep(delay)
                        continue
                    else:
                        self.thread_safe_log(f"❌ 所有重试都失败，HTTP状态码: {response.status_code}")
                        return {}
                
                # 尝试解析JSON响应
                try:
                    response_data = response.json()
                    self.thread_safe_log(f"✅ 成功解析JSON响应")
                except json.JSONDecodeError as e:
                    self.thread_safe_log(f"❌ JSON解析失败: {str(e)}")
                    self.thread_safe_log(f"📄 原始响应: {response.text[:500]}...")
                    
                    if attempt < max_retries - 1:
                        delay = self._get_saiyii_retry_delay(attempt, "json_error", is_price_api)
                        self.thread_safe_log(f"⏰ JSON解析错误等待 {delay} 秒后重试...")
                        time.sleep(delay)
                        continue
                    else:
                        self.thread_safe_log(f"❌ 所有重试都失败，无法解析JSON响应")
                        return {}
                
                # 检查API返回的业务状态码
                result_code = response_data.get("Result")
                if result_code != 1:
                    error_message = response_data.get("Message", "无错误信息")
                    self.thread_safe_log(f"❌ 赛盈API业务错误，Result={result_code}")
                    self.thread_safe_log(f"📄 错误信息: {error_message}")
                    
                    # 某些业务错误不需要重试（如参数错误、权限错误等）
                    if self._is_retryable_saiyii_error(result_code, error_message):
                        if attempt < max_retries - 1:
                            delay = self._get_saiyii_retry_delay(attempt, "business_error", is_price_api)
                            self.thread_safe_log(f"⏰ 业务错误可重试，等待 {delay} 秒后重试...")
                            time.sleep(delay)
                            continue
                        else:
                            self.thread_safe_log(f"❌ 所有重试都失败，业务错误: {error_message}")
                            return {}
                    else:
                        self.thread_safe_log(f"❌ 业务错误不可重试: {error_message}")
                        return {}
                
                # 解析成功的响应
                parsed_result = self._parse_saiyii_response(response_data)
                self.thread_safe_log(f"🎯 赛盈API请求成功完成")
                return parsed_result
                
            except requests.exceptions.Timeout as e:
                self.thread_safe_log(f"⏰ 赛盈API请求超时: {str(e)}")
                if attempt < max_retries - 1:
                    delay = self._get_saiyii_retry_delay(attempt, "timeout", is_price_api)
                    self.thread_safe_log(f"⏰ 超时错误等待 {delay} 秒后重试...")
                    time.sleep(delay)
                    continue
                    
            except requests.exceptions.ConnectionError as e:
                self.thread_safe_log(f"🔌 赛盈API连接错误: {str(e)}")
                if attempt < max_retries - 1:
                    delay = self._get_saiyii_retry_delay(attempt, "connection_error", is_price_api)
                    self.thread_safe_log(f"⏰ 连接错误等待 {delay} 秒后重试...")
                    time.sleep(delay)
                    continue
                    
            except requests.exceptions.RequestException as e:
                self.thread_safe_log(f"📡 赛盈API请求异常: {str(e)}")
                if attempt < max_retries - 1:
                    delay = self._get_saiyii_retry_delay(attempt, "request_error", is_price_api)
                    self.thread_safe_log(f"⏰ 请求异常等待 {delay} 秒后重试...")
                    time.sleep(delay)
                    continue
                    
            except Exception as e:
                self.thread_safe_log(f"❌ 赛盈API未知异常: {str(e)}")
                import traceback
                self.thread_safe_log(f"📋 异常详情: {traceback.format_exc()}")
                if attempt < max_retries - 1:
                    delay = self._get_saiyii_retry_delay(attempt, "unknown_error", is_price_api)
                    self.thread_safe_log(f"⏰ 未知异常等待 {delay} 秒后重试...")
                    time.sleep(delay)
                    continue
        
        # 所有重试都失败
        self.thread_safe_log(f"❌ 赛盈API请求失败: 经过 {max_retries} 次重试后仍无法获取响应")
        return {}
    
    def _get_saiyii_retry_delay(self, attempt, error_type, is_price_api=False):
        """获取赛盈API重试延迟时间（针对不同错误类型和API类型优化）"""
        base_delay = 1.0
        
        if is_price_api:
            # 价格API使用更快的重试策略
            if error_type in ["connection_error", "timeout"]:
                # 网络相关错误快速重试
                delays = [0.5, 1.0, 2.0]  # 0.5秒, 1秒, 2秒
            elif error_type in ["http_error", "json_error"]:
                # HTTP和JSON错误中等延迟
                delays = [1.0, 1.5, 3.0]  # 1秒, 1.5秒, 3秒
            elif error_type == "business_error":
                # 业务错误稍长延迟
                delays = [1.5, 2.5, 4.0]  # 1.5秒, 2.5秒, 4秒
            else:
                # 未知错误使用默认延迟
                delays = [1.0, 2.0, 3.0]  # 1秒, 2秒, 3秒
        else:
            # 产品详情API使用标准重试策略
            if error_type in ["connection_error", "timeout"]:
                # 网络相关错误
                delays = [1.0, 2.0, 4.0]  # 1秒, 2秒, 4秒
            elif error_type in ["http_error", "json_error"]:
                # HTTP和JSON错误
                delays = [1.5, 3.0, 5.0]  # 1.5秒, 3秒, 5秒
            elif error_type == "business_error":
                # 业务错误
                delays = [2.0, 4.0, 6.0]  # 2秒, 4秒, 6秒
            else:
                # 未知错误使用指数退避（原有逻辑）
                return min(2 ** attempt, 8)
        
        # 返回对应尝试次数的延迟，如果超出数组范围则返回最后一个值
        return delays[min(attempt, len(delays) - 1)]
    
    def _is_retryable_saiyii_error(self, result_code, error_message):
        """判断赛盈API错误是否可以重试"""
        # 不可重试的错误类型
        non_retryable_keywords = [
            "参数错误", "参数不正确", "无效参数", "权限", "认证", "签名", 
            "token", "unauthorized", "forbidden", "invalid parameter",
            "参数缺失", "格式错误", "SKU不存在", "产品不存在"
        ]
        
        error_message_lower = str(error_message).lower()
        
        # 检查是否包含不可重试的关键词
        for keyword in non_retryable_keywords:
            if keyword.lower() in error_message_lower:
                self.thread_safe_log(f"🚫 检测到不可重试错误关键词: {keyword}")
                return False
        
        # 特定的结果码判断
        if result_code in [0, -1, -2, -3]:  # 假设这些是可重试的系统错误
            self.thread_safe_log(f"🔄 系统错误可重试，Result={result_code}")
            return True
        
        # 默认情况下，尝试重试一次
        self.thread_safe_log(f"🤔 未知错误类型，尝试重试，Result={result_code}")
        return True
    
    def get_saiyii_product_detail(self, skus):
        """获取赛盈平台产品详情"""
        self.thread_safe_log(f"📦 开始获取 {len(skus)} 个SKU的产品详情")
        start_time = time.time()
        
        data = {"Skus": skus}
        result = self._saiyii_request("POST", data, "/api/Product/QueryProductDetail")
        
        elapsed_time = time.time() - start_time
        self.thread_safe_log(f"📦 产品详情获取完成，耗时 {elapsed_time:.2f} 秒")
        
        if result and "ProductInfoList" in result:
            product_count = len(result["ProductInfoList"])
            self.thread_safe_log(f"✅ 产品详情API成功返回 {product_count} 个产品信息")
        else:
            self.thread_safe_log(f"⚠️ 产品详情API未返回有效数据")
        
        return result
    
    def get_saiyii_product_price(self, skus):
        """获取赛盈平台产品价格"""
        self.thread_safe_log(f"💰 开始获取 {len(skus)} 个SKU的价格信息（优化重试策略）")
        start_time = time.time()
        
        data = {"SkuList": skus}
        result = self._saiyii_request("POST", data, "/api/Product/QueryProductPriceV2")
        
        elapsed_time = time.time() - start_time
        self.thread_safe_log(f"💰 价格获取完成，耗时 {elapsed_time:.2f} 秒")
        
        if result:
            self.thread_safe_log(f"✅ 价格API成功返回数据")
        else:
            self.thread_safe_log(f"⚠️ 价格API未返回有效数据，将在产品处理中使用空价格")
        
        return result
    
    def retry_request(self, method, url, max_retries=3, **kwargs):
        """简单的重试请求实现"""
        self.thread_safe_log(f"🔄 开始重试请求: {method} {url}")
        
        for attempt in range(max_retries):
            try:
                self.thread_safe_log(f"🌐 尝试 {attempt + 1}/{max_retries} 发送请求...")
                response = requests.request(method, url, **kwargs)
                
                self.thread_safe_log(f"📥 响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    self.thread_safe_log(f"✅ 请求成功")
                    return response
                else:
                    self.thread_safe_log(f"❌ 请求失败，状态码: {response.status_code}")
                    self.thread_safe_log(f"📄 响应内容: {response.text[:500]}...")
                    
            except Exception as e:
                self.thread_safe_log(f"❌ 请求异常: {str(e)}")
                import traceback
                self.thread_safe_log(f"📋 异常详情: {traceback.format_exc()}")
                
            if attempt < max_retries - 1:
                delay = 2 ** attempt
                self.thread_safe_log(f"⏰ 等待 {delay} 秒后重试...")
                time.sleep(delay)  # 指数退避
                
        self.thread_safe_log(f"❌ 所有重试都失败了")
        return None
        
    def run(self):
        """爬取数据的主要逻辑"""
        try:
            # 自动分组SKU到不同平台
            giga_skus, saiyii_skus = self.categorize_skus(self.sku_list)
            
            self.log_message.emit(f"SKU分析完成:")
            self.log_message.emit(f"  GIGA平台: {len(giga_skus)} 个SKU")
            self.log_message.emit(f"  赛盈平台: {len(saiyii_skus)} 个SKU")
            
            if self.use_multithread:
                self.log_message.emit(f"GIGA平台使用多线程模式，并发数: {self.max_workers}")
            else:
                self.log_message.emit("GIGA平台使用单线程模式")
            
            self.log_message.emit("赛盈平台固定使用单线程API调用模式")
            
            # 设置总进度计算参数
            self.total_skus = len(self.sku_list)
            self.processed_skus = 0
            
            all_data = []
            
            # 初始化失败SKU统计
            failed_giga_skus = []
            failed_saiyii_skus = []
            successful_giga_skus = []
            successful_saiyii_skus = []
            
            # 处理GIGA平台SKU
            if giga_skus:
                if self.use_multithread:
                    self.log_message.emit("开始多线程处理GIGA平台SKU...")
                    giga_data, giga_success_skus, giga_failed_skus = self.run_giga_scraping_multithread(giga_skus)
                else:
                    self.log_message.emit("开始单线程处理GIGA平台SKU...")
                    giga_data, giga_success_skus, giga_failed_skus = self.run_giga_scraping_single_thread(giga_skus)
                
                all_data.extend(giga_data)
                successful_giga_skus = giga_success_skus
                failed_giga_skus = giga_failed_skus
            
            # 处理赛盈平台SKU（使用单线程API调用）
            if saiyii_skus:
                self.log_message.emit("开始处理赛盈平台SKU（单线程API调用模式）...")
                saiyii_data, saiyii_success_skus, saiyii_failed_skus = self.run_saiyii_scraping(saiyii_skus)
                all_data.extend(saiyii_data)
                successful_saiyii_skus = saiyii_success_skus
                failed_saiyii_skus = saiyii_failed_skus
            
            # 确保进度达到100%
            self.progress_updated.emit(100)
            
            # 统计和打印最终结果
            self.print_final_statistics(
                all_data, 
                giga_skus, saiyii_skus,
                successful_giga_skus, failed_giga_skus,
                successful_saiyii_skus, failed_saiyii_skus
            )
            
            self.scraping_finished.emit(all_data)
                
        except Exception as e:
            self.error_occurred.emit(f"爬取过程中发生错误: {str(e)}")
    
    def print_final_statistics(self, all_data, giga_skus, saiyii_skus, 
                              successful_giga_skus, failed_giga_skus,
                              successful_saiyii_skus, failed_saiyii_skus):
        """打印最终统计信息"""
        self.log_message.emit("=" * 80)
        self.log_message.emit("🎯 爬取完成 - 最终统计报告")
        self.log_message.emit("=" * 80)
        
        # 总体统计
        total_input_skus = len(giga_skus) + len(saiyii_skus)
        total_successful = len(successful_giga_skus) + len(successful_saiyii_skus)
        total_failed = len(failed_giga_skus) + len(failed_saiyii_skus)
        success_rate = (total_successful / total_input_skus * 100) if total_input_skus > 0 else 0
        
        self.log_message.emit(f"📊 总体统计:")
        self.log_message.emit(f"  • 输入SKU总数: {total_input_skus}")
        self.log_message.emit(f"  • 成功获取数据: {total_successful} 个")
        self.log_message.emit(f"  • 失败SKU数量: {total_failed} 个")
        self.log_message.emit(f"  • 成功率: {success_rate:.1f}%")
        self.log_message.emit(f"  • 实际获取数据条数: {len(all_data)}")
        
        # GIGA平台统计
        if giga_skus:
            giga_success_rate = (len(successful_giga_skus) / len(giga_skus) * 100) if giga_skus else 0
            self.log_message.emit(f"")
            self.log_message.emit(f"🌐 GIGA平台统计:")
            self.log_message.emit(f"  • 输入SKU: {len(giga_skus)} 个")
            self.log_message.emit(f"  • 成功: {len(successful_giga_skus)} 个")
            self.log_message.emit(f"  • 失败: {len(failed_giga_skus)} 个")
            self.log_message.emit(f"  • 成功率: {giga_success_rate:.1f}%")
            
            # GIGA平台SKU对比分析
            self.log_message.emit(f"  📋 输入SKU列表: {', '.join(giga_skus)}")
            if successful_giga_skus:
                self.log_message.emit(f"  ✅ 成功SKU列表: {', '.join(successful_giga_skus)}")
            if failed_giga_skus:
                self.log_message.emit(f"  ❌ 失败SKU列表: {', '.join(failed_giga_skus)}")
            
            # 检查GIGA平台遗漏SKU
            giga_processed = set(successful_giga_skus + failed_giga_skus)
            giga_input_set = set(giga_skus)
            giga_missing = giga_input_set - giga_processed
            
            if giga_missing:
                self.log_message.emit(f"  🔍 GIGA平台遗漏SKU: {', '.join(giga_missing)}")
                # 补充到失败列表
                failed_giga_skus.extend(list(giga_missing))
                self.log_message.emit(f"  🔴 已补充到失败列表")
        
        # 赛盈平台统计
        if saiyii_skus:
            saiyii_success_rate = (len(successful_saiyii_skus) / len(saiyii_skus) * 100) if saiyii_skus else 0
            self.log_message.emit(f"")
            self.log_message.emit(f"🏪 赛盈平台统计:")
            self.log_message.emit(f"  • 输入SKU: {len(saiyii_skus)} 个")
            self.log_message.emit(f"  • 成功: {len(successful_saiyii_skus)} 个")
            self.log_message.emit(f"  • 失败: {len(failed_saiyii_skus)} 个")
            self.log_message.emit(f"  • 成功率: {saiyii_success_rate:.1f}%")
            
            # 赛盈平台SKU对比分析
            self.log_message.emit(f"  📋 输入SKU列表: {', '.join(saiyii_skus)}")
            if successful_saiyii_skus:
                self.log_message.emit(f"  ✅ 成功SKU列表: {', '.join(successful_saiyii_skus)}")
            if failed_saiyii_skus:
                self.log_message.emit(f"  ❌ 失败SKU列表: {', '.join(failed_saiyii_skus)}")
            
            # 检查赛盈平台遗漏SKU
            saiyii_processed = set(successful_saiyii_skus + failed_saiyii_skus)
            saiyii_input_set = set(saiyii_skus)
            saiyii_missing = saiyii_input_set - saiyii_processed
            
            if saiyii_missing:
                self.log_message.emit(f"  🔍 赛盈平台遗漏SKU: {', '.join(saiyii_missing)}")
                self.log_message.emit(f"  🔍 遗漏SKU详情: 这些SKU在输入中但未被处理")
                # 补充到失败列表
                failed_saiyii_skus.extend(list(saiyii_missing))
                self.log_message.emit(f"  🔴 已补充到失败列表")
            
            # 验证赛盈平台统计完整性
            total_saiyii_processed = len(successful_saiyii_skus) + len(failed_saiyii_skus)
            self.log_message.emit(f"  📊 验证: 输入{len(saiyii_skus)} = 成功{len(successful_saiyii_skus)} + 失败{len(failed_saiyii_skus)} = 处理{total_saiyii_processed}")
            
            if total_saiyii_processed != len(saiyii_skus):
                self.log_message.emit(f"  ⚠️ 赛盈平台统计不一致！需要进一步检查")
        
        # 重新计算总统计（包含补充的遗漏SKU）
        total_successful_final = len(successful_giga_skus) + len(successful_saiyii_skus)
        total_failed_final = len(failed_giga_skus) + len(failed_saiyii_skus)
        
        # 汇总失败SKU
        all_failed_skus = failed_giga_skus + failed_saiyii_skus
        
        if all_failed_skus:
            self.log_message.emit(f"")
            self.log_message.emit(f"💀 所有失败SKU汇总 (共{len(all_failed_skus)}个):")
            failed_chunks = [all_failed_skus[i:i+15] for i in range(0, len(all_failed_skus), 15)]
            for chunk in failed_chunks:
                self.log_message.emit(f"   {', '.join(chunk)}")
        else:
            self.log_message.emit(f"")
            self.log_message.emit(f"🎉 恭喜！所有SKU都已成功处理！")
        
        # 最终验证
        self.log_message.emit(f"")
        self.log_message.emit(f"🔍 最终统计验证:")
        self.log_message.emit(f"  输入总数: {total_input_skus}")
        self.log_message.emit(f"  成功总数: {total_successful_final}")
        self.log_message.emit(f"  失败总数: {total_failed_final}")
        self.log_message.emit(f"  处理总数: {total_successful_final + total_failed_final}")
        
        if (total_successful_final + total_failed_final) == total_input_skus:
            self.log_message.emit(f"  ✅ 统计完整，所有SKU都已正确分类")
        else:
            self.log_message.emit(f"  ❌ 统计仍有问题，请检查代码逻辑")
        
        self.log_message.emit("=" * 80)
    
    def thread_safe_log(self, message):
        """线程安全的日志记录"""
        self.log_message.emit(message)
    
    def thread_safe_update_progress(self):
        """线程安全的进度更新"""
        self.mutex.lock()
        try:
            self.processed_skus += 1
            if self.total_skus > 0:
                progress = int((self.processed_skus / self.total_skus) * 100)
                self.progress_updated.emit(progress)
        finally:
            self.mutex.unlock()
    
    def categorize_skus(self, sku_list):
        """根据SKU格式自动分类到不同平台"""
        giga_skus = []
        saiyii_skus = []
        
        for sku in sku_list:
            if self.is_giga_sku(sku):
                giga_skus.append(sku)
            elif self.is_saiyii_sku(sku):
                saiyii_skus.append(sku)
            else:
                # 默认归类到GIGA平台
                self.log_message.emit(f"未识别的SKU格式: {sku}，默认归类到GIGA平台")
                giga_skus.append(sku)
        
        return giga_skus, saiyii_skus
    
    def is_giga_sku(self, sku):
        """判断是否为GIGA平台SKU"""
        import re
        # GIGA平台特征：包含字母，或以字母开头，或包含特殊字符
        return bool(re.search(r'[A-Za-z]', sku)) or '.' in sku
    
    def is_saiyii_sku(self, sku):
        """判断是否为赛盈平台SKU"""
        # 赛盈平台特征：纯数字，通常6-10位
        if not sku.isdigit():
            return False
        
        sku_len = len(sku)
        # 赛盈平台SKU长度通常为6-10位纯数字
        return 6 <= sku_len <= 10
    
    def run_giga_scraping_multithread(self, giga_skus):
        """GIGA平台多线程爬取逻辑"""
        # 从数据库获取OCSESSID
        self.log_message.emit("正在从数据库获取GIGA平台认证信息...")
        self.ocsessid = fetch_data_from_mysql('giga_token_yijiandaifa').replace('OCSESSID=', '')
        if not self.ocsessid:
            self.error_occurred.emit("无法从数据库获取GIGA平台认证信息，请检查数据库连接和配置")
            return [], [], []
        
        self.log_message.emit("成功获取GIGA平台认证信息")
        
        all_data = []
        successful_skus = []
        failed_skus = []
        
        self.log_message.emit(f"开始多线程处理GIGA平台 {len(giga_skus)} 个SKU")
        
        # 使用线程池处理SKU
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_sku = {
                executor.submit(self.fetch_product_data_threaded, sku): sku 
                for sku in giga_skus
            }
            
            # 收集结果
            for future in as_completed(future_to_sku):
                if not self.is_running:
                    self.thread_safe_log("用户中止了爬取过程")
                    # 取消剩余的任务
                    for f in future_to_sku:
                        f.cancel()
                    break
                
                sku = future_to_sku[future]
                try:
                    result_dict = future.result()
                    if result_dict:
                        all_data.append(result_dict)
                        successful_skus.append(sku)
                        self.thread_safe_log(f"成功获取 SKU {sku} 的数据")
                    else:
                        failed_skus.append(sku)
                        self.thread_safe_log(f"未找到 SKU {sku} 的数据")
                except Exception as e:
                    failed_skus.append(sku)
                    self.thread_safe_log(f"处理 SKU {sku} 时发生错误: {str(e)}")
                
                # 更新进度
                self.thread_safe_update_progress()
        
        self.log_message.emit(f"GIGA平台多线程爬取完成，共获取 {len(all_data)} 条数据")
        return all_data, successful_skus, failed_skus

    def run_saiyii_scraping(self, saiyii_skus):
        """赛盈平台爬取逻辑 - 使用单线程API调用"""
        self.log_message.emit("开始处理赛盈平台SKU（单线程API调用模式）...")
        total_skus = len(saiyii_skus)
        
        if total_skus == 0:
            return [], [], []
        
        self.log_message.emit(f"赛盈平台API调用 - 共 {total_skus} 个SKU，使用智能批量处理")
        self.log_message.emit("API调用模式：稳定可靠，批量处理，遵循API限制")
        
        all_data = []
        successful_skus = []
        failed_skus = []
        
        # 批量处理SKU，每次最多30个（API限制）
        batch_size = 30
        total_batches = (total_skus + batch_size - 1) // batch_size
        
        self.log_message.emit(f"将分成 {total_batches} 个批次处理，每批最多 {batch_size} 个SKU")
        
        for batch_index in range(total_batches):
            if not self.is_running:
                self.log_message.emit("用户中止了爬取过程")
                break
            
            start_idx = batch_index * batch_size
            end_idx = min(start_idx + batch_size, total_skus)
            batch_skus = saiyii_skus[start_idx:end_idx]
            
            self.log_message.emit(f"正在处理批次 {batch_index + 1}/{total_batches}，包含 {len(batch_skus)} 个SKU")
            self.log_message.emit(f"  批次SKU范围: {start_idx + 1}-{end_idx}")
            self.log_message.emit(f"  批次SKU列表: {', '.join(batch_skus)}")
            
            # 批次级别的重试机制
            batch_success = False
            batch_max_retries = 2  # 批次最大重试次数
            
            for batch_attempt in range(batch_max_retries):
                if not self.is_running:
                    self.log_message.emit("用户中止了爬取过程")
                    break
                
                try:
                    if batch_attempt > 0:
                        self.log_message.emit(f"  🔄 批次重试 {batch_attempt + 1}/{batch_max_retries}")
                        # 优化的批次重试延迟策略
                        if batch_attempt == 1:
                            retry_delay = 2.0  # 第一次重试快速响应
                        else:
                            retry_delay = 5.0  # 第二次重试稍长延迟
                        self.log_message.emit(f"  ⏰ 批次重试前等待 {retry_delay:.1f} 秒...")
                        time.sleep(retry_delay)
                    
                    self.log_message.emit(f"  🔍 正在调用赛盈API获取产品详情...")
                    product_data = self.get_saiyii_product_detail(batch_skus)
                    
                    # 检查产品详情是否获取成功
                    if not product_data:
                        self.log_message.emit(f"  ❌ 获取产品详情失败，数据为空")
                        if batch_attempt < batch_max_retries - 1:
                            self.log_message.emit(f"  🔄 将进行批次重试...")
                            continue
                        else:
                            # 整个批次失败，所有SKU都标记为失败
                            for sku in batch_skus:
                                if sku not in successful_skus and sku not in failed_skus:
                                    failed_skus.append(sku)
                            self.log_message.emit(f"  ❌ 批次 {batch_index + 1} 最终失败，所有SKU标记为失败")
                            break
                    
                    if "ProductInfoList" not in product_data:
                        self.log_message.emit(f"  ❌ API返回数据中缺少ProductInfoList字段")
                        self.log_message.emit(f"  📄 完整返回数据: {product_data}")
                        if batch_attempt < batch_max_retries - 1:
                            self.log_message.emit(f"  🔄 将进行批次重试...")
                            continue
                        else:
                            # 整个批次失败，所有SKU都标记为失败
                            for sku in batch_skus:
                                if sku not in successful_skus and sku not in failed_skus:
                                    failed_skus.append(sku)
                            self.log_message.emit(f"  ❌ 批次 {batch_index + 1} 最终失败，所有SKU标记为失败")
                            break
                    
                    product_list = product_data["ProductInfoList"]
                    self.log_message.emit(f"  📦 API返回数据结构: {list(product_data.keys())}")
                    self.log_message.emit(f"  📋 ProductInfoList长度: {len(product_list)}")
                    
                    # 批量获取价格信息
                    self.log_message.emit(f"  💰 正在批量获取 {len(batch_skus)} 个SKU的价格信息...")
                    price_data = self.get_saiyii_product_price(batch_skus)
                    
                    # 检查价格数据是否获取成功
                    if not price_data:
                        self.log_message.emit(f"  ⚠️ 获取价格信息失败，将使用无价格数据处理")
                        price_dict = {}
                    else:
                        # 创建价格字典
                        price_dict = self.create_price_dict(price_data)
                        self.log_message.emit(f"  📊 成功创建价格字典，包含 {len(price_dict)} 个SKU的价格信息")
                    
                    # 提取产品信息（传入价格字典）
                    extracted_data, processed_skus = self.extract_saiyii_product_info(product_data, price_dict, batch_skus)
                    
                    if extracted_data:
                        all_data.extend(extracted_data)
                        
                        success_count = len(extracted_data)
                        self.log_message.emit(f"  ✓ 批次 {batch_index + 1} 成功获取 {success_count}/{len(batch_skus)} 个产品信息")
                        
                        # 使用extract方法返回的processed_skus来确定成功和失败的SKU
                        batch_failed_skus = [sku for sku in batch_skus if sku not in processed_skus]
                        
                        # 添加到总的成功/失败列表
                        for sku in processed_skus:
                            if sku not in successful_skus:
                                successful_skus.append(sku)
                                self.log_message.emit(f"  ✅ 记录成功SKU: {sku}")
                        
                        for sku in batch_failed_skus:
                            if sku not in failed_skus:
                                failed_skus.append(sku)
                                self.log_message.emit(f"  ❌ 记录失败SKU: {sku}")
                        
                        if batch_failed_skus:
                            self.log_message.emit(f"  ⚠ 批次 {batch_index + 1} 失败SKU: {', '.join(batch_failed_skus)}")
                        
                        # 验证批次统计
                        batch_success_count = len(processed_skus)
                        batch_failure_count = len(batch_failed_skus)
                        batch_total = batch_success_count + batch_failure_count
                        self.log_message.emit(f"  📊 批次统计验证: 输入{len(batch_skus)} = 成功{batch_success_count} + 失败{batch_failure_count} = 总计{batch_total}")
                        
                        if batch_total != len(batch_skus):
                            self.log_message.emit(f"  ⚠️ 批次统计不匹配！需要修正")
                            # 查找遗漏的SKU
                            accounted_skus = set(processed_skus + batch_failed_skus)
                            batch_skus_set = set(batch_skus)
                            missing_batch_skus = batch_skus_set - accounted_skus
                            if missing_batch_skus:
                                self.log_message.emit(f"  🔍 批次遗漏SKU: {', '.join(missing_batch_skus)}")
                                for sku in missing_batch_skus:
                                    if sku not in failed_skus:
                                        failed_skus.append(sku)
                                        self.log_message.emit(f"  🔴 补充失败SKU: {sku}")
                        
                        batch_success = True
                        break  # 批次成功，跳出重试循环
                    else:
                        self.log_message.emit(f"  ❌ 未能提取任何产品信息")
                        if batch_attempt < batch_max_retries - 1:
                            self.log_message.emit(f"  🔄 将进行批次重试...")
                            continue
                        else:
                            # 整个批次失败，所有SKU都标记为失败
                            for sku in batch_skus:
                                if sku not in successful_skus and sku not in failed_skus:
                                    failed_skus.append(sku)
                                    self.log_message.emit(f"  🔴 批次失败-标记失败SKU: {sku}")
                            self.log_message.emit(f"  ❌ 批次 {batch_index + 1} 最终失败，所有SKU标记为失败")
                            break
                    
                except Exception as e:
                    self.log_message.emit(f"  ❌ 批次 {batch_index + 1} 处理异常: {str(e)}")
                    import traceback
                    self.log_message.emit(f"  📋 异常详情: {traceback.format_exc()}")
                    
                    if batch_attempt < batch_max_retries - 1:
                        self.log_message.emit(f"  🔄 异常后将进行批次重试...")
                        continue
                    else:
                        # 异常导致整个批次失败
                        for sku in batch_skus:
                            if sku not in successful_skus and sku not in failed_skus:
                                failed_skus.append(sku)
                                self.log_message.emit(f"  🔴 批次失败-标记失败SKU: {sku}")
                        self.log_message.emit(f"  ❌ 批次 {batch_index + 1} 最终失败，所有SKU标记为失败")
                        break
            
            if not batch_success:
                self.log_message.emit(f"  💀 批次 {batch_index + 1} 在 {batch_max_retries} 次重试后仍然失败")
                # 确保失败的SKU被记录（防止重复添加）
                for sku in batch_skus:
                    if sku not in successful_skus and sku not in failed_skus:
                        failed_skus.append(sku)
                        self.log_message.emit(f"  🔴 添加失败SKU: {sku}")
            
            # 更新进度
            for _ in batch_skus:
                self.thread_safe_update_progress()
            
            # 添加智能延迟避免请求过快
            if batch_index < total_batches - 1:  # 最后一批不需要延迟
                delay_time = min(self.delay, 2.0)  # 最大延迟2秒
                self.log_message.emit(f"  等待 {delay_time:.1f} 秒后处理下一批次...")
                time.sleep(delay_time)
        
        # 统计验证
        success_count = len(successful_skus)
        failure_count = len(failed_skus)
        
        # 验证统计完整性
        total_processed = success_count + failure_count
        if total_processed != total_skus:
            self.log_message.emit(f"⚠️ 统计不一致检测:")
            self.log_message.emit(f"  输入总数: {total_skus}")
            self.log_message.emit(f"  成功数量: {success_count}")
            self.log_message.emit(f"  失败数量: {failure_count}")
            self.log_message.emit(f"  处理总数: {total_processed}")
            
            # 查找遗漏的SKU
            all_processed_skus = set(successful_skus + failed_skus)
            input_skus_set = set(saiyii_skus)
            missing_skus = input_skus_set - all_processed_skus
            
            if missing_skus:
                self.log_message.emit(f"  🔍 发现遗漏的SKU: {', '.join(missing_skus)}")
                # 将遗漏的SKU标记为失败
                for sku in missing_skus:
                    failed_skus.append(sku)
                    self.log_message.emit(f"  🔴 遗漏SKU标记为失败: {sku}")
                
                # 重新计算
                failure_count = len(failed_skus)
                total_processed = success_count + failure_count
                self.log_message.emit(f"  ✅ 修正后统计: 成功{success_count} + 失败{failure_count} = 总计{total_processed}")
        
        self.log_message.emit(f"赛盈平台爬取完成！")
        self.log_message.emit(f"  总计: {total_skus} 个SKU | 成功: {success_count} | 失败: {failure_count}")
        self.log_message.emit(f"  实际获取数据条数: {len(all_data)}")
        
        return all_data, successful_skus, failed_skus
    
    def create_price_dict(self, price_data):
        """创建价格字典，以SKU为key，价格信息为value"""
        price_dict = {}
        
        if not price_data or "DataList" not in price_data:
            self.thread_safe_log("⚠️ 价格数据为空或格式异常")
            return price_dict
        
        data_list = price_data["DataList"]
        self.thread_safe_log(f"📊 开始处理 {len(data_list)} 个SKU的价格数据...")
        
        for sku_price_data in data_list:
            try:
                sku = sku_price_data.get("Sku", "")
                if not sku:
                    continue
                
                warehouse_price_list = sku_price_data.get("WarehousePriceList", [])
                if not warehouse_price_list:
                    self.thread_safe_log(f"⚠️ SKU {sku} 无价格数据")
                    continue
                
                # 查找符合条件的价格信息：CurrencyCode=USD且LogisticsProductName=Standard Shipping
                target_price_info = None
                for price_info in warehouse_price_list:
                    if (price_info.get("CurrencyCode") == "USD" and 
                        price_info.get("LogisticsProductName") == "Standard Shipping"):
                        target_price_info = price_info
                        break
                
                # 如果没有找到符合条件的，使用第一个价格信息
                if not target_price_info and warehouse_price_list:
                    target_price_info = warehouse_price_list[0]
                
                if target_price_info:
                    selling_price = target_price_info.get("SellingPrice")
                    currency_code = target_price_info.get("CurrencyCode", "USD")
                    
                    if selling_price:
                        price_dict[sku] = {
                            "purchase_price": round(selling_price, 2),
                            "declared_value": round(selling_price * 0.6, 2),
                            "currency_code": currency_code,
                            "logistics": target_price_info.get("LogisticsProductName", ""),
                            "raw_price_info": target_price_info
                        }
                        self.thread_safe_log(f"✅ SKU {sku}: ${selling_price} {currency_code}")
                    else:
                        self.thread_safe_log(f"⚠️ SKU {sku} 价格数据中未找到SellingPrice")
                else:
                    self.thread_safe_log(f"❌ SKU {sku} 无有效价格信息")
                    
            except Exception as e:
                self.thread_safe_log(f"❌ 处理SKU价格数据时发生错误: {str(e)}")
        
        self.thread_safe_log(f"🎯 价格字典创建完成，成功处理 {len(price_dict)}/{len(data_list)} 个SKU")
        return price_dict
    
    def extract_saiyii_product_info(self, data, price_dict=None, batch_skus=None):
        """提取赛盈平台产品信息"""
        self.thread_safe_log("🔍 开始提取赛盈平台产品信息...")
        extracted_data = []
        processed_skus = []  # 跟踪已处理的SKU
        
        if not data.get("ProductInfoList"):
            self.thread_safe_log("❌ ProductInfoList为空或不存在")
            return extracted_data, processed_skus
        
        self.thread_safe_log(f"📋 准备处理 {len(data['ProductInfoList'])} 个产品")
        if batch_skus:
            self.thread_safe_log(f"📋 批次期望SKU: {', '.join(batch_skus)}")
        
        for index, product in enumerate(data["ProductInfoList"]):
            try:
                sku = product.get("Sku", "")
                self.thread_safe_log(f"🔄 正在处理产品 {index + 1}: SKU {sku}")
                
                if not sku:
                    self.thread_safe_log(f"  ⚠️ 产品 {index + 1} 没有SKU信息，跳过")
                    continue
                
                # 查找主图片（IsMainImage为True的图片）
                main_image_url = None
                goods_image_list = product.get("GoodsImageList", [])
                
                # 首先尝试获取主图片
                for image in goods_image_list:
                    if image.get("IsMainImage"):
                        main_image_url = image.get("ImageUrl")
                        self.thread_safe_log(f"  📸 找到主图片: {sku}")
                        break
                
                # 如果没有找到主图片，获取第一张图片
                if not main_image_url and goods_image_list:
                    main_image_url = goods_image_list[0].get("ImageUrl")
                    self.thread_safe_log(f"  📸 使用第一张图片: {sku}")
                
                if not main_image_url:
                    self.thread_safe_log(f"  ⚠️ 未找到图片: {sku}")
                
                # 从价格字典获取价格信息
                purchase_price = None
                declared_value = None
                currency_code = "USD"
                
                if price_dict and sku in price_dict:
                    price_info = price_dict[sku]
                    purchase_price = price_info["purchase_price"]
                    declared_value = price_info["declared_value"]
                    currency_code = price_info["currency_code"]
                    self.thread_safe_log(f"  💰 从价格字典获取SKU {sku} 价格: ${purchase_price} {currency_code}")
                else:
                    self.thread_safe_log(f"  ⚠️ 未找到SKU {sku} 的价格信息")
                
                # 组装结果
                product_info = {
                    'SKU': sku,
                    '产品名称': product.get("CnName", ""),
                    '产品英文名称': product.get("EnName", ""),
                    '毛重(kg)': round(product.get("SpecWeight", 0) / 1000, 2),  # 重量（G）转毛重(kg)
                    '包装长(cm)': product.get("SpecLength", 0),
                    '包装宽(cm)': product.get("SpecWidth", 0),
                    '包装高(cm)': product.get("SpecHeight", 0),
                    '采购价': purchase_price,
                    '采购币种': currency_code,
                    '默认供应商代码': "赛盈GT",
                    '英文申报品名': '',
                    '中文申报品名': '',
                    '申报价值(美元)': declared_value,
                    '产品品类(分类代码)': product.get("CategoryThirdName", ""),
                    '产品销售状态': '',
                    '销售负责人': '',
                    '开发负责人': '',
                    '附属销售员': '',
                    '设计师': '',
                    '运营方式': '',
                    '自定义分类': '赛盈GT',
                    '图片URL': main_image_url or ""
                }
                
                extracted_data.append(product_info)
                processed_skus.append(sku)
                self.thread_safe_log(f"  ✅ 成功处理产品 {index + 1}: SKU {sku}")
                
            except Exception as e:
                sku = product.get("Sku", f"未知产品{index + 1}")
                self.thread_safe_log(f"  ❌ 处理产品 {index + 1} (SKU: {sku}) 时发生错误: {str(e)}")
                import traceback
                self.thread_safe_log(f"  📋 错误详情: {traceback.format_exc()}")
        
        self.thread_safe_log(f"🎯 产品信息提取完成，成功处理 {len(extracted_data)}/{len(data['ProductInfoList'])} 个产品")
        self.thread_safe_log(f"🎯 已处理SKU列表: {', '.join(processed_skus)}")
        
        # 如果提供了批次SKU列表，检查是否有遗漏
        if batch_skus:
            missing_skus = [sku for sku in batch_skus if sku not in processed_skus]
            if missing_skus:
                self.thread_safe_log(f"⚠️ 批次中未在ProductInfoList中找到的SKU: {', '.join(missing_skus)}")
        
        return extracted_data, processed_skus
    
    def fetch_product_data(self, sku):
        """获取单个SKU的商品数据"""
        max_retries = 3  # 最大重试次数
        retry_delay = 2  # 重试延迟(秒)
        
        # 检查是否需要停止
        if not self.is_running:
            return None
            
        try:
            cookies = {
                'OCSESSID': self.ocsessid
            }

            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'cache-control': 'no-cache',
                'content-type': 'application/json',
                'ori-status-in-response': 'code',
                'origin': 'https://www.gigab2b.com',
                'pragma': 'no-cache',
                'priority': 'u=1, i',
                'referer': f'https://www.gigab2b.com/index.php?route=product/search&search={sku}&search_source=4&search_dimension=1',
                'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
                'x-gmd-page-url': f'https://www.gigab2b.com/index.php?route=product/search&search={sku}&search_source=4&search_dimension=1',
                'x-gmd-referer-url': f'https://www.gigab2b.com/index.php?route=product/search&search={sku}&search_source=4&search_dimension=1&dimension_type=1',
                'x-gmd-screen': '2560x1440',
                'x-requested-with': 'XMLHttpRequest, XMLHttpRequest',
            }

            json_data = {
                'page': 1,
                'limit': 100,
                'dimension_type': 1,
                'scene': 1,
                'search': sku,  # 使用传入的SKU
                'sort': '',
                'order': '',
            }

            # 第一步：搜索商品获取product_id（带重试）
            self.log_message.emit(f"正在搜索商品: {sku}")
            product_id = None
            
            for attempt in range(1, max_retries + 1):
                try:
                    # 检查是否需要停止
                    if not self.is_running:
                        return None
                        
                    response = requests.post(
                        self.url,
                        cookies=cookies,
                        headers=headers,
                        json=json_data,
                        timeout=15
                    )
                    
                    if response.status_code != 200:
                        self.log_message.emit(f"搜索请求失败: HTTP状态码 {response.status_code}，尝试 {attempt}/{max_retries}")
                        if attempt < max_retries:
                            time.sleep(retry_delay)
                            continue
                        return None
                    
                    json_obj = json.loads(response.text)
                    if json_obj.get('code') != 200:
                        self.log_message.emit(f"搜索结果异常: {json_obj.get('message', '未知错误')}，尝试 {attempt}/{max_retries}")
                        if attempt < max_retries:
                            time.sleep(retry_delay)
                            continue
                        return None
                    
                    product_list = json_obj.get('data', {}).get('product_list', [])
                    if not product_list:
                        self.log_message.emit(f"未找到商品: {sku}，该商品不存在，跳过处理")
                        return None
                    
                    product_id = product_list[0]
                    self.log_message.emit(f"找到商品ID: {product_id}")
                    break  # 成功获取，跳出重试循环
                    
                except (requests.exceptions.RequestException, json.JSONDecodeError) as e:
                    self.log_message.emit(f"请求错误: {str(e)}，尝试 {attempt}/{max_retries}")
                    if attempt < max_retries:
                        time.sleep(retry_delay)
                        continue
                    return None
            
            if not product_id:
                self.log_message.emit(f"在多次尝试后仍未找到商品: {sku}")
                return None

            # 第二步：获取商品基本信息（带重试）
            product_info_url = f"https://www.gigab2b.com/index.php?route=/product/info/info/baseInfos&product_id={product_id}"
            self.log_message.emit(f"正在获取商品基本信息")
            product_info_response = None
            
            for attempt in range(1, max_retries + 1):
                try:
                    # 检查是否需要停止
                    if not self.is_running:
                        return None
                        
                    product_info_response = requests.post(
                        url=product_info_url,
                        cookies=cookies,
                        headers=headers,
                        json=json_data,
                        timeout=15
                    )
                    
                    if product_info_response.status_code != 200:
                        self.log_message.emit(f"获取商品信息失败: HTTP状态码 {product_info_response.status_code}，尝试 {attempt}/{max_retries}")
                        if attempt < max_retries:
                            time.sleep(retry_delay)
                            continue
                        return None
                    
                    # 尝试解析JSON
                    json_product_info = json.loads(product_info_response.text)
                    if json_product_info.get('code') != 200:
                        self.log_message.emit(f"获取商品信息返回错误: {json_product_info.get('message', '未知错误')}，尝试 {attempt}/{max_retries}")
                        if attempt < max_retries:
                            time.sleep(retry_delay)
                            continue
                    
                    break  # 成功获取，跳出重试循环
                    
                except (requests.exceptions.RequestException, json.JSONDecodeError) as e:
                    self.log_message.emit(f"获取商品信息错误: {str(e)}，尝试 {attempt}/{max_retries}")
                    if attempt < max_retries:
                        time.sleep(retry_delay)
                        continue
                    return None

            # 第三步：获取商品价格信息（带重试）
            product_price_url = f"https://www.gigab2b.com/index.php?route=/product/info/price/list&product_id={product_id}"
            self.log_message.emit(f"正在获取商品价格信息")
            product_price_response = None
            
            for attempt in range(1, max_retries + 1):
                try:
                    # 检查是否需要停止
                    if not self.is_running:
                        return None
                        
                    product_price_response = requests.post(
                        url=product_price_url,
                        cookies=cookies,
                        headers=headers,
                        json=json_data,
                        timeout=15
                    )
                    
                    if product_price_response.status_code != 200:
                        self.log_message.emit(f"获取价格信息失败: HTTP状态码 {product_price_response.status_code}，尝试 {attempt}/{max_retries}")
                        if attempt < max_retries:
                            time.sleep(retry_delay)
                            continue
                        return None
                    
                    # 尝试解析JSON
                    json_product_price = json.loads(product_price_response.text)
                    if json_product_price.get('code') != 200:
                        self.log_message.emit(f"获取价格信息返回错误: {json_product_price.get('message', '未知错误')}，尝试 {attempt}/{max_retries}")
                        if attempt < max_retries:
                            time.sleep(retry_delay)
                            continue
                    
                    break  # 成功获取，跳出重试循环
                    
                except (requests.exceptions.RequestException, json.JSONDecodeError) as e:
                    self.log_message.emit(f"获取价格信息错误: {str(e)}，尝试 {attempt}/{max_retries}")
                    if attempt < max_retries:
                        time.sleep(retry_delay)
                        continue
                    return None

            # 解析商品信息和价格
            json_product_info = json.loads(product_info_response.text)
            json_product_price = json.loads(product_price_response.text)

            # 检查返回码
            if json_product_info.get('code') != 200 or json_product_price.get('code') != 200:
                self.log_message.emit("获取商品详细信息失败")
                return None

            # 提取基本信息
            product_data = json_product_info.get('data', {}).get('product_info', {})
            actual_sku = product_data.get('sku', '无')
            product_name = product_data.get('product_name', '无')
            main_image = product_data.get('main_image', {}).get('popup', '无')

            # 提取包装尺寸信息
            package_size = product_data.get('specification', {}).get('package_size', {})
            
            # 尝试从combo中获取尺寸，如果没有则使用general
            combo = package_size.get('combo', [])
            if combo:
                first_combo = combo[0]
                length = float(first_combo.get('length', 0))
                width = float(first_combo.get('width', 0))
                height = float(first_combo.get('height', 0))
                weight = float(first_combo.get('weight', 0))
            else:
                general = package_size.get('general', {})
                length = float(general.get('length', 0))
                width = float(general.get('width', 0))
                height = float(general.get('height', 0))
                weight = float(general.get('weight', 0))

            # 获取价格
            price = json_product_price.get('data', {}).get('base_price_info', {}).get('price', 0)

            # 组装结果
            result_dict = {
                'SKU': sku,
                '产品名称':"",    
                '产品英文名称': product_name,
                '毛重(kg)': round(weight*0.454, 2),
                '包装长(cm)': round(length*2.54, 2),
                '包装宽(cm)': round(width*2.54, 2),
                '包装高(cm)': round(height*2.54, 2),
                '采购价': round(price, 2),
                '采购币种':"USD",
                '默认供应商代码':"GIGA",
                '英文申报品名':'',
                '中文申报品名':'',
                '申报价值(美元)': round(price*0.6, 2),
                '产品品类(分类代码)':'',
                '产品销售状态':'',
                '销售负责人':'',
                '开发负责人':'',
                '附属销售员':'',
                '设计师':'',
                '运营方式':'',
                '自定义分类':'GIGA;自提GIGA',
                '图片URL':main_image
            }

            return result_dict

        except Exception as e:
            self.log_message.emit(f"处理SKU {sku} 时发生错误: {str(e)}")
            return None
    
    def fetch_product_data_threaded(self, sku):
        """多线程版本的商品数据获取"""
        # 添加随机延迟，避免请求过于集中
        import random
        delay = self.delay + random.uniform(0, 0.5)
        time.sleep(delay)
        
        return self.fetch_product_data(sku)

    def run_giga_scraping_single_thread(self, giga_skus):
        """GIGA平台单线程爬取逻辑（原有逻辑，保留备用）"""
        # 从数据库获取OCSESSID
        self.log_message.emit("正在从数据库获取GIGA平台认证信息...")
        self.ocsessid = fetch_data_from_mysql('giga_token_yijiandaifa').replace('OCSESSID=', '')
        if not self.ocsessid:
            self.error_occurred.emit("无法从数据库获取GIGA平台认证信息，请检查数据库连接和配置")
            return [], [], []
        
        self.log_message.emit("成功获取GIGA平台认证信息")
        
        all_data = []
        successful_skus = []
        failed_skus = []
        
        self.log_message.emit(f"开始单线程处理GIGA平台 {len(giga_skus)} 个SKU")
        
        for i, sku in enumerate(giga_skus):
            if not self.is_running:
                self.log_message.emit("用户中止了爬取过程")
                break
            
            self.log_message.emit(f"正在处理 SKU: {sku} ({i+1}/{len(giga_skus)})")
            
            # 添加延迟，避免请求过快
            time.sleep(self.delay)
            
            # 获取商品数据
            result_dict = self.fetch_product_data(sku)
            
            if result_dict:
                all_data.append(result_dict)
                successful_skus.append(sku)
                self.log_message.emit(f"成功获取 SKU {sku} 的数据")
            else:
                failed_skus.append(sku)
                self.log_message.emit(f"未找到 SKU {sku} 的数据")
            
            # 更新总体进度
            self.thread_safe_update_progress()
        
        self.log_message.emit(f"GIGA平台单线程爬取完成，共获取 {len(all_data)} 条数据")
        return all_data, successful_skus, failed_skus
    
    def stop(self):
        """停止爬取"""
        self.is_running = False
    
    def update_progress(self):
        """更新总体进度"""
        if hasattr(self, 'total_skus') and self.total_skus > 0:
            progress = int((self.processed_skus / self.total_skus) * 100)
            self.progress_updated.emit(progress)


class WebScraperApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.scraped_data = []
        self.scraper_thread = None

        # 初始化配置管理
        self.settings = QSettings("DataScrapingTool", "Config")
        self.load_config()

        self.init_ui()

    def load_config(self):
        """加载配置"""
        # 赛盈平台API配置
        self.saiyii_base_url = self.settings.value("saiyii/base_url", "https://api.saleyee.com")
        self.saiyii_app_token = self.settings.value("saiyii/app_token", "ZocsKv7BS5vH3orQGxDQOQ==")
        self.saiyii_app_key = self.settings.value("saiyii/app_key", "SnBIRFqo")

        # GIGA平台配置
        self.giga_base_url = self.settings.value("giga/base_url", "https://www.gigab2b.com/index.php?route=/product/list/search")
        self.giga_delay = float(self.settings.value("giga/delay", 2.0))
        self.giga_max_retries = int(self.settings.value("giga/max_retries", 3))
        self.giga_debug = self.settings.value("giga/debug", False, type=bool)
        self.giga_cache = self.settings.value("giga/cache", True, type=bool)

        # 通用配置
        default_path = os.path.join(os.path.expanduser("~"), "Desktop", "scraped_data.xlsx")
        self.default_save_path = self.settings.value("general/default_save_path", default_path)
        self.auto_save_interval = int(self.settings.value("general/auto_save_interval", 0))
        self.show_welcome = self.settings.value("general/show_welcome", True, type=bool)
        self.auto_scroll_log = self.settings.value("general/auto_scroll_log", True, type=bool)
        self.remember_window_size = self.settings.value("general/remember_window_size", True, type=bool)

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("🚀 智能多平台数据爬取工具 - GIGA & 赛盈平台集成版")
        self.setGeometry(100, 100, 1600, 1000)  # 进一步增大窗口尺寸
        self.setMinimumSize(1200, 800)  # 设置最小尺寸

        # 设置窗口图标（如果有的话）
        # self.setWindowIcon(QIcon("icon.png"))

        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪 - 请输入SKU开始处理")

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局 - 使用分割器实现可调整的布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 创建顶部工具栏区域
        self.create_toolbar()
        main_layout.addWidget(self.toolbar_frame)

        # 创建主要内容区域（使用分割器）
        main_splitter = QSplitter(Qt.Horizontal)
        main_splitter.setChildrenCollapsible(False)

        # 左侧控制面板
        left_panel = self.create_control_panel()
        main_splitter.addWidget(left_panel)

        # 右侧数据展示区域
        right_panel = self.create_data_panel()
        main_splitter.addWidget(right_panel)

        # 设置分割器比例 (30% : 70%)
        main_splitter.setSizes([480, 1120])
        main_layout.addWidget(main_splitter)

        # 初始化完成后的设置
        self.setup_initial_state()

    def create_toolbar(self):
        """创建顶部工具栏"""
        self.toolbar_frame = QFrame()
        self.toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        self.toolbar_frame.setMaximumHeight(80)
        self.toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f0f0f0);
                border: 1px solid #d0d0d0;
                border-radius: 8px;
                margin: 5px;
            }
        """)

        toolbar_layout = QHBoxLayout(self.toolbar_frame)
        toolbar_layout.setContentsMargins(20, 15, 20, 15)

        # 应用标题和图标
        title_layout = QVBoxLayout()
        title_label = QLabel("🚀 智能多平台数据爬取工具")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin: 0;")

        subtitle_label = QLabel("支持 GIGA & 赛盈平台 | 智能识别 | 批量处理")
        subtitle_label.setFont(QFont("Microsoft YaHei", 10))
        subtitle_label.setStyleSheet("color: #7f8c8d; margin: 0;")

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        title_layout.setSpacing(2)

        toolbar_layout.addLayout(title_layout)
        toolbar_layout.addStretch()

        # 快速操作按钮
        quick_actions_layout = QHBoxLayout()
        quick_actions_layout.setSpacing(10)

        # 帮助按钮
        help_button = QPushButton("❓ 帮助")
        help_button.setToolTip("查看使用说明和平台支持信息")
        help_button.clicked.connect(self.show_help)
        help_button.setStyleSheet(self.get_button_style("#3498db"))

        # 设置按钮
        settings_button = QPushButton("⚙️ 设置")
        settings_button.setToolTip("配置高级选项")
        settings_button.clicked.connect(self.show_settings)
        settings_button.setStyleSheet(self.get_button_style("#9b59b6"))

        quick_actions_layout.addWidget(help_button)
        quick_actions_layout.addWidget(settings_button)

        toolbar_layout.addLayout(quick_actions_layout)

    def create_control_panel(self):
        """创建左侧控制面板"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        control_layout.setSpacing(15)
        control_layout.setContentsMargins(10, 10, 10, 10)

        # SKU输入区域
        sku_group = self.create_modern_group("📝 SKU输入", "输入要处理的SKU列表")
        sku_layout = QVBoxLayout(sku_group)

        # SKU输入提示
        sku_hint = QLabel("💡 支持的格式：")
        sku_hint.setFont(QFont("Microsoft YaHei", 9))
        sku_hint.setStyleSheet("color: #7f8c8d; margin-bottom: 5px;")
        sku_layout.addWidget(sku_hint)

        hint_text = QLabel("• GIGA: W1239138471, DL000649AAB\n• 赛盈: 32791415, 04156467")
        hint_text.setFont(QFont("Consolas", 8))
        hint_text.setStyleSheet("color: #95a5a6; margin-bottom: 10px; padding: 5px; background: #f8f9fa; border-radius: 3px;")
        sku_layout.addWidget(hint_text)

        self.sku_input = QTextEdit()
        self.sku_input.setPlaceholderText("请输入SKU，每行一个...\n\n示例：\nW1239138471\n32791415\nDL000649AAB")
        self.sku_input.setMaximumHeight(150)
        self.sku_input.setFont(QFont("Consolas", 10))
        self.sku_input.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px;
                background-color: #ffffff;
                selection-background-color: #3498db;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)
        sku_layout.addWidget(self.sku_input)

        control_layout.addWidget(sku_group)

        # 处理配置区域
        config_group = self.create_modern_group("⚙️ 处理配置", "配置爬取参数")
        config_layout = QVBoxLayout(config_group)

        # 多线程配置
        thread_frame = QFrame()
        thread_frame.setStyleSheet("QFrame { background: #f8f9fa; border-radius: 6px; padding: 5px; }")
        thread_layout = QGridLayout(thread_frame)

        thread_layout.addWidget(QLabel("🔄 并发模式:"), 0, 0)
        self.enable_multithread = QComboBox()
        self.enable_multithread.addItems(["🚀 启用多线程", "🐌 单线程模式"])
        self.enable_multithread.setCurrentText("🚀 启用多线程")
        self.enable_multithread.currentTextChanged.connect(self.on_thread_mode_changed)
        self.enable_multithread.setStyleSheet(self.get_combobox_style())
        thread_layout.addWidget(self.enable_multithread, 0, 1)

        thread_layout.addWidget(QLabel("🔢 线程数:"), 1, 0)
        self.thread_count_spinbox = QSpinBox()
        self.thread_count_spinbox.setRange(1, 10)
        self.thread_count_spinbox.setValue(3)
        self.thread_count_spinbox.setStyleSheet(self.get_spinbox_style())
        thread_layout.addWidget(self.thread_count_spinbox, 1, 1)

        self.thread_info_label = QLabel("💡 多线程模式 - 仅影响GIGA平台，建议3-5个线程")
        self.thread_info_label.setFont(QFont("Microsoft YaHei", 8))
        self.thread_info_label.setStyleSheet("color: #7f8c8d; margin-top: 5px;")
        self.thread_info_label.setWordWrap(True)
        thread_layout.addWidget(self.thread_info_label, 2, 0, 1, 2)

        config_layout.addWidget(thread_frame)
        control_layout.addWidget(config_group)

        # 文件保存配置
        file_group = self.create_modern_group("💾 保存配置", "设置输出文件路径")
        file_layout = QVBoxLayout(file_group)

        file_path_layout = QHBoxLayout()
        self.file_path_input = QLineEdit()
        self.file_path_input.setPlaceholderText("选择保存路径...")
        self.file_path_input.setStyleSheet(self.get_lineedit_style())

        self.browse_button = QPushButton("📁 浏览")
        self.browse_button.clicked.connect(self.browse_save_path)
        self.browse_button.setStyleSheet(self.get_button_style("#e67e22"))

        file_path_layout.addWidget(self.file_path_input)
        file_path_layout.addWidget(self.browse_button)
        file_layout.addLayout(file_path_layout)

        control_layout.addWidget(file_group)

        # 控制按钮区域
        button_group = self.create_modern_group("🎮 操作控制", "开始或停止处理")
        button_layout = QVBoxLayout(button_group)

        # 主要操作按钮
        main_buttons_layout = QHBoxLayout()

        self.start_button = QPushButton("🚀 开始爬取")
        self.start_button.clicked.connect(self.start_scraping)
        self.start_button.setStyleSheet(self.get_primary_button_style())
        self.start_button.setMinimumHeight(45)

        self.stop_button = QPushButton("⏹️ 停止爬取")
        self.stop_button.clicked.connect(self.stop_scraping)
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet(self.get_danger_button_style())
        self.stop_button.setMinimumHeight(45)

        main_buttons_layout.addWidget(self.start_button)
        main_buttons_layout.addWidget(self.stop_button)
        button_layout.addLayout(main_buttons_layout)

        # 辅助按钮
        aux_buttons_layout = QHBoxLayout()

        self.clear_button = QPushButton("🗑️ 清空日志")
        self.clear_button.clicked.connect(self.clear_log)
        self.clear_button.setStyleSheet(self.get_button_style("#95a5a6"))

        self.export_button = QPushButton("📊 导出Excel")
        self.export_button.clicked.connect(self.export_data)
        self.export_button.setEnabled(False)
        self.export_button.setStyleSheet(self.get_button_style("#27ae60"))

        aux_buttons_layout.addWidget(self.clear_button)
        aux_buttons_layout.addWidget(self.export_button)
        button_layout.addLayout(aux_buttons_layout)

        control_layout.addWidget(button_group)

        # 进度显示区域
        progress_group = self.create_modern_group("📊 处理进度", "实时显示处理状态")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setStyleSheet(self.get_progressbar_style())
        self.progress_bar.setMinimumHeight(25)
        progress_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("就绪")
        self.status_label.setFont(QFont("Microsoft YaHei", 10))
        self.status_label.setStyleSheet("color: #2c3e50; font-weight: bold; margin-top: 5px;")
        self.status_label.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(self.status_label)

        control_layout.addWidget(progress_group)

        # 添加弹性空间
        control_layout.addStretch()

        return control_widget

    def create_data_panel(self):
        """创建右侧数据展示面板"""
        data_widget = QWidget()
        data_layout = QVBoxLayout(data_widget)
        data_layout.setSpacing(10)
        data_layout.setContentsMargins(10, 10, 10, 10)

        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(self.get_tab_style())

        # 日志标签页
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)

        # 日志工具栏
        log_toolbar = QFrame()
        log_toolbar.setMaximumHeight(40)
        log_toolbar.setStyleSheet("QFrame { background: #f8f9fa; border-radius: 5px; }")
        log_toolbar_layout = QHBoxLayout(log_toolbar)

        log_title = QLabel("📋 实时日志")
        log_title.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        log_title.setStyleSheet("color: #2c3e50;")
        log_toolbar_layout.addWidget(log_title)
        log_toolbar_layout.addStretch()

        auto_scroll_label = QLabel("🔄 自动滚动")
        auto_scroll_label.setStyleSheet("color: #7f8c8d; font-size: 9px;")
        log_toolbar_layout.addWidget(auto_scroll_label)

        log_layout.addWidget(log_toolbar)

        self.log_text = QTextEdit()
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: #ffffff;
                padding: 10px;
                line-height: 1.4;
            }
        """)
        log_layout.addWidget(self.log_text)

        self.tab_widget.addTab(log_tab, "📋 处理日志")

        # 数据预览标签页
        preview_tab = QWidget()
        preview_layout = QVBoxLayout(preview_tab)

        # 数据统计工具栏
        stats_toolbar = QFrame()
        stats_toolbar.setMaximumHeight(50)
        stats_toolbar.setStyleSheet("QFrame { background: #f8f9fa; border-radius: 5px; padding: 5px; }")
        stats_layout = QHBoxLayout(stats_toolbar)

        stats_title = QLabel("📊 数据统计")
        stats_title.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        stats_title.setStyleSheet("color: #2c3e50;")
        stats_layout.addWidget(stats_title)

        stats_layout.addStretch()

        self.data_stats_label = QLabel("暂无数据")
        self.data_stats_label.setFont(QFont("Microsoft YaHei", 9))
        self.data_stats_label.setStyleSheet("color: #7f8c8d; background: #ffffff; padding: 5px 10px; border-radius: 3px;")
        stats_layout.addWidget(self.data_stats_label)

        preview_layout.addWidget(stats_toolbar)

        # 数据表格
        self.preview_table = QTableWidget()
        self.preview_table.setFont(QFont("Microsoft YaHei", 9))
        self.preview_table.setStyleSheet(self.get_table_style())

        # 设置表格属性
        self.preview_table.setAlternatingRowColors(True)
        self.preview_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.preview_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.preview_table.setSortingEnabled(True)

        # 设置表头
        header = self.preview_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setStretchLastSection(True)

        preview_layout.addWidget(self.preview_table)

        self.tab_widget.addTab(preview_tab, "📊 数据预览")

        data_layout.addWidget(self.tab_widget)

        return data_widget

    def create_modern_group(self, title, description=""):
        """创建现代化的分组框"""
        group = QGroupBox(title)
        group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                margin: 8px 0px;
                padding-top: 15px;
                background-color: #ffffff;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                background-color: #ffffff;
            }}
        """)

        if description:
            group.setToolTip(description)

        return group

    def get_button_style(self, color="#3498db"):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background-color: {color};
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 11px;
                font-weight: bold;
                border-radius: 6px;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """

    def get_primary_button_style(self):
        """获取主要按钮样式"""
        return """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #2ecc71);
                border: none;
                color: white;
                padding: 12px 24px;
                text-align: center;
                font-size: 12px;
                font-weight: bold;
                border-radius: 8px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #229954, stop:1 #27ae60);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e8449, stop:1 #229954);
            }
            QPushButton:disabled {
                background: #bdc3c7;
                color: #7f8c8d;
            }
        """

    def get_danger_button_style(self):
        """获取危险按钮样式"""
        return """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                border: none;
                color: white;
                padding: 12px 24px;
                text-align: center;
                font-size: 12px;
                font-weight: bold;
                border-radius: 8px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #a93226, stop:1 #922b21);
            }
            QPushButton:disabled {
                background: #bdc3c7;
                color: #7f8c8d;
            }
        """

    def get_combobox_style(self):
        """获取下拉框样式"""
        return """
            QComboBox {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px 12px;
                background-color: white;
                font-size: 10px;
                min-width: 120px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7f8c8d;
                margin-right: 5px;
            }
        """

    def get_spinbox_style(self):
        """获取数字输入框样式"""
        return """
            QSpinBox {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px 8px;
                background-color: white;
                font-size: 10px;
                min-width: 60px;
            }
            QSpinBox:focus {
                border-color: #3498db;
            }
        """

    def get_lineedit_style(self):
        """获取文本输入框样式"""
        return """
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px 12px;
                background-color: white;
                font-size: 10px;
                selection-background-color: #3498db;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """

    def get_progressbar_style(self):
        """获取进度条样式"""
        return """
            QProgressBar {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 11px;
                background-color: #f8f9fa;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 6px;
                margin: 2px;
            }
        """

    def get_table_style(self):
        """获取表格样式"""
        return """
            QTableWidget {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: #e0e0e0;
                selection-background-color: #3498db;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f2f6);
                padding: 8px;
                border: 1px solid #e0e0e0;
                font-weight: bold;
                color: #2c3e50;
            }
        """

    def get_tab_style(self):
        """获取标签页样式"""
        return """
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
                color: #495057;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-bottom-color: white;
                color: #2c3e50;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f3f4);
            }
        """

    def darken_color(self, color, factor=0.9):
        """使颜色变暗"""
        if color.startswith('#'):
            color = color[1:]

        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)

        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)

        return f"#{r:02x}{g:02x}{b:02x}"

    def setup_initial_state(self):
        """设置初始状态"""
        # 设置默认保存路径（使用配置中的路径）
        self.file_path_input.setText(self.default_save_path)

        # 显示欢迎信息（根据配置决定是否显示）
        if self.show_welcome:
            self.log_message("🎉 智能多平台数据爬取工具已启动")
            self.log_message("🔍 系统支持自动识别平台:")
            self.log_message("  📦 GIGA平台 - 包含字母的SKU（如W1239138471, DL000649AAB）")
            self.log_message("    └─ 🚀 支持多线程并发处理，提高爬取效率")
            self.log_message("  🏪 赛盈平台 - 6-10位纯数字SKU（如32791415, 04156467）")
            self.log_message("    └─ 🔧 使用单线程API调用，每批最多30个SKU，稳定可靠")
            self.log_message("✨ 已集成赛盈平台官方API，支持自动获取产品详情和价格信息")
            self.log_message("🚀 请配置并发设置并输入SKU信息开始处理")
            self.log_message("💡 提示：可以通过顶部的'⚙️ 设置'按钮配置API参数")

        # 设置标签页默认选中日志页
        self.tab_widget.setCurrentIndex(0)

    def show_help(self):
        """显示帮助信息"""
        help_text = """
        <h3>🚀 智能多平台数据爬取工具 - 使用指南</h3>

        <h4>📋 支持的平台：</h4>
        <ul>
        <li><b>GIGA平台</b>：支持包含字母的SKU（如：W1239138471, DL000649AAB）</li>
        <li><b>赛盈平台</b>：支持6-10位纯数字SKU（如：32791415, 04156467）</li>
        </ul>

        <h4>⚙️ 配置说明：</h4>
        <ul>
        <li><b>多线程模式</b>：仅影响GIGA平台，建议3-5个线程</li>
        <li><b>单线程模式</b>：所有平台均使用单线程，适合网络不稳定时使用</li>
        </ul>

        <h4>📝 输入格式：</h4>
        <ul>
        <li>每行输入一个SKU</li>
        <li>系统会自动识别平台类型</li>
        <li>支持批量处理，无数量限制</li>
        </ul>

        <h4>💾 输出说明：</h4>
        <ul>
        <li>支持导出为Excel格式</li>
        <li>包含完整的产品信息和价格数据</li>
        <li>自动生成统计报告</li>
        </ul>
        """

        QMessageBox.information(self, "📖 使用帮助", help_text)

    def show_settings(self):
        """显示设置对话框"""
        dialog = ConfigDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            # 重新加载配置
            self.load_config()

            # 更新界面上的默认保存路径
            self.file_path_input.setText(self.default_save_path)

            # 显示配置更新成功的消息
            self.log_message("⚙️ 配置已更新并保存")

            # 如果有需要，可以在这里重新初始化某些组件
            QMessageBox.information(self, "设置", "✅ 配置已成功更新！")

    def browse_save_path(self):
        """浏览保存路径"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "选择保存路径",
            self.file_path_input.text(),
            "Excel文件 (*.xlsx);;所有文件 (*)"
        )
        if file_path:
            self.file_path_input.setText(file_path)

    
    def get_sku_data(self):
        """获取SKU输入框的数据"""
        return self.sku_input.toPlainText().strip()
    
    def start_scraping(self):
        """开始爬取数据"""
        try:
            sku_data = self.get_sku_data()
            if not sku_data:
                QMessageBox.warning(self, "警告", "请输入SKU信息")
                return

            # 解析SKU数据（用户可以根据需要自定义这部分逻辑）
            sku_list = [line.strip() for line in sku_data.split('\n') if line.strip()]

            # 获取多线程配置
            is_multithread = self.enable_multithread.currentText() == "🚀 启用多线程"
            max_workers = self.thread_count_spinbox.value()

            if is_multithread:
                self.log_message(f"🚀 准备使用多线程模式处理 {len(sku_list)} 个SKU，并发数: {max_workers}")
            else:
                self.log_message(f"🐌 准备使用单线程模式处理 {len(sku_list)} 个SKU")

            # 重置界面状态
            self.progress_bar.setValue(0)
            self.scraped_data = []
            self.preview_table.setRowCount(0)
            self.data_stats_label.setText("正在处理...")
            self.export_button.setEnabled(False)

            # 创建并启动爬取线程（使用配置中的参数）
            base_url = self.giga_base_url
            delay = self.giga_delay

            # 如果之前有线程，先清理
            if hasattr(self, 'scraper_thread') and self.scraper_thread:
                try:
                    self.scraper_thread.finished.disconnect()
                except:
                    pass  # 忽略断开连接时的错误

            self.scraper_thread = WebScraperThread(base_url, delay, max_workers)
            self.scraper_thread.sku_list = sku_list  # 设置SKU列表
            self.scraper_thread.use_multithread = is_multithread  # 设置是否使用多线程

            # 传递配置参数给线程
            self.scraper_thread.saiyii_base_url = self.saiyii_base_url
            self.scraper_thread.saiyii_app_token = self.saiyii_app_token
            self.scraper_thread.saiyii_app_key = self.saiyii_app_key
            self.scraper_thread.giga_max_retries = self.giga_max_retries
            self.scraper_thread.giga_debug = self.giga_debug

            self.scraper_thread.progress_updated.connect(self.update_progress)
            self.scraper_thread.log_message.connect(self.log_message)
            self.scraper_thread.scraping_finished.connect(self.on_scraping_finished)
            self.scraper_thread.error_occurred.connect(self.on_error)

            self.scraper_thread.start()

            # 更新按钮状态
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.export_button.setEnabled(False)
            self.status_label.setText("处理中...")

        except KeyboardInterrupt:
            self.log_message("用户中断了操作")
            self.reset_ui_state()
        except Exception as e:
            self.log_message(f"启动爬取时发生错误: {str(e)}")
            self.reset_ui_state()
            QMessageBox.critical(self, "错误", f"启动爬取时发生错误:\n{str(e)}")
    
    def stop_scraping(self):
        """停止爬取"""
        if self.scraper_thread and self.scraper_thread.isRunning():
            self.log_message("正在停止爬取，请稍候...")
            self.stop_button.setEnabled(False)  # 防止重复点击
            self.stop_button.setText("停止中...")
            
            # 设置停止标志
            self.scraper_thread.stop()
            
            # 使用非阻塞方式等待线程结束
            self.scraper_thread.finished.connect(self.on_thread_finished)
            
            # 设置超时定时器，如果5秒内没有停止，强制结束
            self.stop_timer = QTimer()
            self.stop_timer.timeout.connect(self.force_stop_thread)
            self.stop_timer.setSingleShot(True)
            self.stop_timer.start(5000)  # 5秒超时
        else:
            self.reset_ui_state()
    
    def on_thread_finished(self):
        """线程结束时的回调"""
        if hasattr(self, 'stop_timer'):
            self.stop_timer.stop()
        self.log_message("处理已停止")
        self.reset_ui_state()
    
    def force_stop_thread(self):
        """强制停止线程"""
        if self.scraper_thread and self.scraper_thread.isRunning():
            self.log_message("强制停止线程...")
            self.scraper_thread.terminate()
            self.scraper_thread.wait(1000)  # 等待1秒
        self.reset_ui_state()
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)

        # 根据配置决定是否自动滚动到底部
        if hasattr(self, 'auto_scroll_log') and self.auto_scroll_log:
            cursor = self.log_text.textCursor()
            cursor.movePosition(cursor.End)
            self.log_text.setTextCursor(cursor)
    
    def on_scraping_finished(self, data):
        """爬取完成后的处理"""
        self.scraped_data = data
        self.progress_bar.setValue(100)
        self.status_label.setText(f"完成 - 共 {len(data)} 条数据")
        
        # 显示数据预览
        self.show_data_preview()
        
        self.reset_ui_state()
    
    def on_error(self, error_message):
        """处理错误"""
        self.log_message(f"错误: {error_message}")
        QMessageBox.critical(self, "错误", error_message)
        self.reset_ui_state()
    
    def reset_ui_state(self):
        """重置UI状态"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.stop_button.setText("停止爬取")  # 重置按钮文本
        if self.status_label.text() == "处理中...":
            self.status_label.setText("就绪")
    
    def show_data_preview(self):
        """显示数据预览"""
        if not self.scraped_data:
            self.preview_table.setRowCount(0)
            self.preview_table.setColumnCount(0)
            self.data_stats_label.setText("暂无数据")
            self.export_button.setEnabled(False)
            return
        
        # 更新统计信息
        stats_text = f"共获取 {len(self.scraped_data)} 条数据"
        if self.scraped_data:
            # 统计成功和失败的数量
            success_count = len([item for item in self.scraped_data if item.get('产品英文名称') and item.get('产品英文名称') != '无'])
            stats_text += f" | 成功: {success_count} 条 | 失败: {len(self.scraped_data) - success_count} 条"
        self.data_stats_label.setText(stats_text)
        
        # 设置表格行列数
        self.preview_table.setRowCount(len(self.scraped_data))
        if self.scraped_data:
            self.preview_table.setColumnCount(len(self.scraped_data[0]))
            
            # 设置表头
            headers = list(self.scraped_data[0].keys())
            self.preview_table.setHorizontalHeaderLabels(headers)
            
            # 填充表格数据
            for row, item in enumerate(self.scraped_data):
                for col, key in enumerate(headers):
                    value = item.get(key, '')  # 使用get方法避免KeyError
                    # 处理长文本，如果超过100字符则截断
                    if isinstance(value, str) and len(value) > 100:
                        value = value[:97] + "..."
                    self.preview_table.setItem(row, col, QTableWidgetItem(str(value)))
            
            # 调整列宽
            self.preview_table.resizeColumnsToContents()
            
            # 设置最小列宽，确保表格美观
            for col in range(self.preview_table.columnCount()):
                if self.preview_table.columnWidth(col) < 80:
                    self.preview_table.setColumnWidth(col, 80)
                elif self.preview_table.columnWidth(col) > 200:
                    self.preview_table.setColumnWidth(col, 200)
        
        # 启用导出按钮
        self.export_button.setEnabled(True)
    
    def export_data(self):
        """导出数据到Excel"""
        if not self.scraped_data:
            QMessageBox.warning(self, "警告", "没有数据可导出")
            return
        
        file_path = self.file_path_input.text().strip()
        if not file_path:
            QMessageBox.warning(self, "警告", "请选择保存路径")
            return
        
        try:
            # 创建DataFrame并保存到Excel
            df = pd.DataFrame(self.scraped_data)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 保存到Excel
            df.to_excel(file_path, index=False, engine='openpyxl')
            
            self.log_message(f"数据已导出到: {file_path}")
            QMessageBox.information(self, "导出成功", f"数据已成功导出到:\n{file_path}\n\n共导出 {len(self.scraped_data)} 条记录")
            
        except Exception as e:
            error_message = f"导出文件时发生错误: {str(e)}"
            self.log_message(error_message)
            QMessageBox.critical(self, "导出失败", error_message)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.log_message("日志已清空")
    
    def on_thread_mode_changed(self, mode):
        """线程模式改变时的回调"""
        if mode == "🚀 启用多线程":
            self.thread_count_spinbox.setEnabled(True)
            self.thread_info_label.setText("💡 多线程模式 - 仅影响GIGA平台，建议3-5个线程")
            self.log_message("🔄 已切换到多线程模式（仅影响GIGA平台，赛盈平台始终使用单线程API）")
        else:
            self.thread_count_spinbox.setEnabled(False)
            self.thread_info_label.setText("💡 单线程模式 - 所有平台均使用单线程，适合网络不稳定时使用")
            self.log_message("🔄 已切换到单线程模式（所有平台均使用单线程处理）")


def fetch_data_from_mysql(key='giga_token_yijiandaifa'):
    """
    从MySQL数据库中获取指定key的配置值

    Returns:
        str or None: 查询结果的值，如果没有找到则返回None
    """
    try:
        connection = pymysql.connect(
            host='*************',
            port=3306,
            user='sgm',
            password='edfp.md4321',
            database='segmart_erp',
            autocommit=True,  # 启用自动提交
            charset='utf8mb4',
            connect_timeout=10  # 设置连接超时
        )

        try:
            with connection.cursor() as cursor:
                # 执行SQL查询
                sql = "SELECT `value` FROM sgm_system_config WHERE `key` = %s"
                cursor.execute(sql, (key,))

                # 获取查询结果
                result = cursor.fetchone()
                if result:
                    # 缓存结果，并去掉字符串的引号
                    cached_value = result[0].strip('"')
                    return cached_value
                else:
                    print(f"未找到键'{key}'的配置值。")
                    return None
        finally:
            # 关闭数据库连接
            connection.close()
    
    except pymysql.Error as e:
        print(f"数据库连接错误: {e}")
        return None
    except Exception as e:
        print(f"获取配置时发生错误: {e}")
        return None


def main():
    try:
        app = QApplication(sys.argv)

        # 设置现代化应用程序样式
        app.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            }
            QWidget {
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            }
            QSplitter::handle {
                background-color: #dee2e6;
                border: 1px solid #adb5bd;
                border-radius: 2px;
            }
            QSplitter::handle:horizontal {
                width: 6px;
                margin: 2px 0px;
            }
            QSplitter::handle:vertical {
                height: 6px;
                margin: 0px 2px;
            }
            QSplitter::handle:hover {
                background-color: #6c757d;
            }
            QStatusBar {
                background-color: #ffffff;
                border-top: 1px solid #dee2e6;
                padding: 5px;
                font-size: 11px;
                color: #495057;
            }
            QScrollBar:vertical {
                border: none;
                background: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #ced4da;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #adb5bd;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
            QToolTip {
                background-color: #343a40;
                color: white;
                border: 1px solid #495057;
                border-radius: 4px;
                padding: 5px;
                font-size: 11px;
            }
        """)

        window = WebScraperApp()
        window.show()

        sys.exit(app.exec_())

    except KeyboardInterrupt:
        print("程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"程序启动时发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()