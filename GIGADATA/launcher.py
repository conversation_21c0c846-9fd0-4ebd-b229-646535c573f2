#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动器 - 用于避免numpy CPU dispatcher冲突
"""

import os
import sys
import multiprocessing

def setup_environment():
    """设置环境变量以避免numpy冲突"""
    # 设置numpy相关环境变量
    os.environ['OPENBLAS_NUM_THREADS'] = '1'
    os.environ['MKL_NUM_THREADS'] = '1'
    os.environ['NUMEXPR_NUM_THREADS'] = '1'
    os.environ['OMP_NUM_THREADS'] = '1'
    os.environ['NUMPY_EXPERIMENTAL_ARRAY_FUNCTION'] = '0'
    os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
    
    # 设置多进程启动方法
    try:
        multiprocessing.set_start_method('spawn', force=True)
    except RuntimeError:
        pass  # 如果已经设置过就忽略

def main():
    """主启动函数"""
    # 设置环境
    setup_environment()

    # 导入并运行主程序
    try:
        from main import main as main_app
        main_app()
    except Exception as e:
        # 在GUI应用中不能使用input()，改用消息框
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox
            import sys

            app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()

            msg = QMessageBox()
            msg.setIcon(QMessageBox.Critical)
            msg.setWindowTitle("程序启动错误")
            msg.setText(f"程序启动失败: {str(e)}")
            msg.setDetailedText(f"详细错误信息:\n{str(e)}")
            msg.exec_()

        except ImportError:
            # 如果PyQt5也无法导入，则静默退出
            pass

if __name__ == "__main__":
    main()
