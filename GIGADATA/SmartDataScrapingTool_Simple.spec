# -*- mode: python ; coding: utf-8 -*-

import sys
import os

# 简化版本的隐藏导入，移除numpy和pandas
hiddenimports = [
    'PyQt5.sip',
    'requests.packages.urllib3.util.ssl_',
    'requests.packages.urllib3.contrib.pyopenssl',
    'bs4',
    'csv',
    'json',
    'threading',
    'concurrent.futures',
    'urllib.parse',
]

a = Analysis(
    ['main_simple.py'],  # 使用简化版本的main
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter', 
        'matplotlib', 
        'IPython', 
        'jupyter', 
        'test', 
        'unittest',
        'numpy',  # 完全排除numpy
        'pandas',  # 完全排除pandas
        'scipy',
        'sklearn',
        'tornado',
        'zmq',
    ],
    noarchive=False,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='SmartDataScrapingTool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # GUI应用
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
