@echo off
chcp 65001 >nul
echo 🚀 开始打包智能多平台数据爬取工具...
echo.

REM 显示系统信息
echo 📋 系统信息:
echo Python版本:
python --version
echo PyInstaller版本:
pyinstaller --version
echo.

REM 检查是否安装了所需依赖
echo 📦 正在检查和安装依赖...
pip install -r requirements.txt --upgrade
echo.

REM 清理之前的打包文件
echo 🧹 清理之前的打包文件...
if exist dist rmdir /s /q dist
if exist build rmdir /s /q build
if exist __pycache__ rmdir /s /q __pycache__
echo.



REM 开始打包
echo 🔨 开始打包程序...
echo 这可能需要几分钟时间，请耐心等待...
pyinstaller build.spec --clean --noconfirm
echo.

REM 检查打包结果
if exist "dist\智能多平台数据爬取工具.exe" (
    echo ✅ 打包成功！
    echo.
    echo 📁 可执行文件位置: dist\智能多平台数据爬取工具.exe
    echo 📊 文件大小:
    for %%A in ("dist\智能多平台数据爬取工具.exe") do echo    %%~zA 字节
    echo.
    echo 📋 使用说明:
    echo    1. 将 dist 文件夹中的所有文件复制到目标电脑
    echo    2. 双击运行 智能多平台数据爬取工具.exe
    echo    3. 无需安装Python环境即可运行
    echo.
    echo 🎉 打包完成！程序已准备好分发。
) else (
    echo ❌ 打包失败，请检查错误信息。
    echo.
    echo 💡 常见问题解决方案:
    echo    1. 确保所有依赖都已正确安装
    echo    2. 检查Python和PyInstaller版本兼容性
    echo    3. 尝试以管理员权限运行此脚本
)

echo.
echo 按任意键退出...
pause >nul