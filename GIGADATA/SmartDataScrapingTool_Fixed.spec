# -*- mode: python ; coding: utf-8 -*-

import sys
import os

# 添加隐藏导入以解决numpy/pandas问题
hiddenimports = [
    'numpy',
    'numpy.core',
    'numpy.core._multiarray_umath',
    'numpy.core.multiarray',
    'numpy.core.umath',
    'pandas',
    'pandas._libs.tslibs.timedeltas',
    'pandas._libs.tslibs.np_datetime',
    'pandas._libs.tslibs.nattype',
    'pandas.io.formats.style',
    'openpyxl.cell._writer',
    'PyQt5.sip',
    'multiprocessing',
    'concurrent.futures',
    'threading',
    'queue',
]

a = Analysis(
    ['launcher.py'],  # 使用launcher作为入口点
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter', 
        'matplotlib', 
        'IPython', 
        'jupyter', 
        'test', 
        'unittest',
        'numpy.distutils',
        'scipy',
        'sklearn',
    ],
    noarchive=False,
)

# 过滤掉可能冲突的模块
a.pure = [x for x in a.pure if not x[0].startswith('numpy.distutils')]
a.pure = [x for x in a.pure if not x[0].startswith('numpy.f2py')]

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='SmartDataScrapingTool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
