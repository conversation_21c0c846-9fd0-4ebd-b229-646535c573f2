@echo off
chcp 65001 >nul
echo 🚀 快速打包智能多平台数据爬取工具
echo ========================================
echo.

REM 切换到脚本所在目录
cd /d "%~dp0"

REM 检查Python环境
echo 📋 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

REM 检查PyInstaller
echo 📦 检查PyInstaller...
pyinstaller --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📥 安装PyInstaller...
    pip install pyinstaller
)

REM 安装依赖
echo 📦 安装依赖包...
pip install -r requirements.txt

REM 清理旧文件
echo 🧹 清理旧文件...
if exist dist rmdir /s /q dist
if exist build rmdir /s /q build

REM 开始打包
echo 🔨 开始打包...
echo 请耐心等待，这可能需要几分钟...
echo.

pyinstaller --onefile --windowed --name "智能多平台数据爬取工具" ^
    --hidden-import=pymysql ^
    --hidden-import=pandas ^
    --hidden-import=numpy ^
    --hidden-import=openpyxl ^
    --hidden-import=requests ^
    --hidden-import=beautifulsoup4 ^
    --hidden-import=lxml ^
    --hidden-import=pycryptodome ^
    --hidden-import=PyQt5.QtCore ^
    --hidden-import=PyQt5.QtGui ^
    --hidden-import=PyQt5.QtWidgets ^
    --hidden-import=PyQt5.sip ^
    --exclude-module=tkinter ^
    --exclude-module=matplotlib ^
    --exclude-module=scipy ^
    main.py

REM 检查结果
echo.
if exist "dist\智能多平台数据爬取工具.exe" (
    echo ✅ 打包成功！
    echo.
    echo 📁 输出文件: dist\智能多平台数据爬取工具.exe
    
    REM 显示文件大小
    for %%A in ("dist\智能多平台数据爬取工具.exe") do (
        set size=%%~zA
        set /a sizeMB=!size!/1024/1024
        echo 📊 文件大小: !sizeMB! MB
    )
    
    echo.
    echo 🎉 打包完成！
    echo 💡 使用说明:
    echo    - 可执行文件位于 dist 文件夹中
    echo    - 可以直接在任何Windows电脑上运行
    echo    - 无需安装Python环境
    
    REM 询问是否打开文件夹
    echo.
    set /p choice="是否打开输出文件夹? (y/n): "
    if /i "%choice%"=="y" (
        explorer dist
    )
) else (
    echo ❌ 打包失败！
    echo 请检查上面的错误信息
)

echo.
pause
