@echo off
echo ========================================
echo    智能多平台数据爬取工具 - 打包程序
echo ========================================
echo.

REM 切换到脚本目录
cd /d "%~dp0"

REM 检查Python环境
echo [1/6] 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: Python未安装或未添加到PATH
    pause
    exit /b 1
)

REM 检查PyInstaller
echo [2/6] 检查PyInstaller...
pyinstaller --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装PyInstaller...
    pip install pyinstaller
)

REM 安装依赖
echo [3/6] 安装依赖包...
pip install -r requirements.txt

REM 清理旧文件
echo [4/6] 清理旧文件...
if exist dist rmdir /s /q dist
if exist build rmdir /s /q build
if exist "*.spec" del "*.spec"

REM 开始打包
echo [5/6] 开始打包程序...
echo 正在生成可执行文件，请耐心等待...
echo.

pyinstaller ^
    --onefile ^
    --windowed ^
    --name "智能多平台数据爬取工具" ^
    --hidden-import=pymysql ^
    --hidden-import=pandas ^
    --hidden-import=numpy ^
    --hidden-import=openpyxl ^
    --hidden-import=requests ^
    --hidden-import=beautifulsoup4 ^
    --hidden-import=lxml ^
    --hidden-import=pycryptodome ^
    --hidden-import=PyQt5.QtCore ^
    --hidden-import=PyQt5.QtGui ^
    --hidden-import=PyQt5.QtWidgets ^
    --hidden-import=PyQt5.sip ^
    --hidden-import=concurrent.futures ^
    --hidden-import=threading ^
    --hidden-import=json ^
    --hidden-import=time ^
    --hidden-import=urllib.parse ^
    --exclude-module=tkinter ^
    --exclude-module=matplotlib ^
    --exclude-module=scipy ^
    --exclude-module=IPython ^
    --exclude-module=jupyter ^
    --clean ^
    --noconfirm ^
    main.py

REM 检查结果
echo.
echo [6/6] 检查打包结果...
if exist "dist\智能多平台数据爬取工具.exe" (
    echo.
    echo ========================================
    echo           打包成功完成！
    echo ========================================
    echo.
    echo 输出文件: dist\智能多平台数据爬取工具.exe
    
    REM 显示文件大小
    for %%A in ("dist\智能多平台数据爬取工具.exe") do (
        set size=%%~zA
        set /a sizeMB=!size!/1024/1024
        echo 文件大小: !sizeMB! MB
    )
    
    echo.
    echo 功能特性:
    echo  ✓ 支持GIGA平台数据爬取
    echo  ✓ 支持赛盈平台API集成
    echo  ✓ 现代化用户界面
    echo  ✓ 多线程并发处理
    echo  ✓ 智能平台识别
    echo  ✓ Excel数据导出
    echo  ✓ 实时进度显示
    echo.
    echo 使用说明:
    echo  1. 将dist文件夹复制到目标电脑
    echo  2. 双击运行"智能多平台数据爬取工具.exe"
    echo  3. 无需安装Python环境即可运行
    echo.
    
    REM 询问是否打开文件夹
    set /p choice="是否打开输出文件夹? (y/n): "
    if /i "%choice%"=="y" (
        explorer dist
    )
    
    echo.
    echo 打包完成！程序已准备好分发。
    
) else (
    echo.
    echo ========================================
    echo           打包失败！
    echo ========================================
    echo.
    echo 可能的解决方案:
    echo  1. 确保所有依赖都已正确安装
    echo  2. 检查Python和PyInstaller版本兼容性
    echo  3. 尝试以管理员权限运行此脚本
    echo  4. 检查磁盘空间是否充足
    echo.
)

echo.
pause
