# -*- mode: python ; coding: utf-8 -*-

import sys
import os

# 创建运行时钩子来设置环境变量
runtime_hook_content = '''
import os
import multiprocessing

# 设置numpy相关环境变量以避免CPU dispatcher冲突
os.environ['OPENBLAS_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['NUMPY_EXPERIMENTAL_ARRAY_FUNCTION'] = '0'

# 设置多进程启动方法
try:
    multiprocessing.set_start_method('spawn', force=True)
except RuntimeError:
    pass  # 如果已经设置过就忽略
'''

# 写入运行时钩子文件
with open('pyi_rth_numpy_fix.py', 'w') as f:
    f.write(runtime_hook_content)

# 添加隐藏导入以解决numpy/pandas问题
hiddenimports = [
    'numpy',
    'numpy.core',
    'numpy.core._multiarray_umath',
    'pandas',
    'pandas._libs.tslibs.timedeltas',
    'pandas._libs.tslibs.np_datetime',
    'pandas._libs.tslibs.nattype',
    'pandas.io.formats.style',
    'openpyxl.cell._writer',
    'PyQt5.sip',
    'multiprocessing',
    'concurrent.futures',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=['pyi_rth_numpy_fix.py'],
    excludes=['tkinter', 'matplotlib', 'IPython', 'jupyter', 'test', 'unittest'],
    noarchive=False,
)

# 过滤掉可能冲突的模块
a.pure = [x for x in a.pure if not x[0].startswith('numpy.distutils')]

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='SmartDataScrapingTool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
